from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QTabWidget, QStatusBar,
                            QToolBar, QMessageBox,
                            QShortcut, QPushButton, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer, QDateTime
from PyQt5.QtGui import QKeySequence

from database import get_session, init_db, User

# تم حذف دالة get_simple_icon - لا تُستخدم بعد الآن


# استيراد واجهات المستخدم المختلفة
from ui.clients import ClientsWidget
from ui.suppliers import SuppliersWidget
from ui.employees import EmployeesWidget
from ui.expenses import ExpensesWidget
from ui.revenues import RevenuesWidget
from ui.invoices import InvoicesWidget
from ui.notifications import NotificationsWidget
from ui.reports import ReportsWidget
from ui.projects import ProjectsWidget


class MainWindow(QMainWindow):
    def __init__(self, session=None, current_user=None):
        super().__init__()

        # تعيين خيارات الأداء العالي
        self.setAttribute(Qt.WA_DeleteOnClose, True)  # تحرير الذاكرة عند إغلاق النافذة
        self.setAttribute(Qt.WA_OpaquePaintEvent, True)  # تحسين أداء الرسم
        self.setAttribute(Qt.WA_NoSystemBackground, True)  # تجنب رسم خلفية النظام
        # لا نستخدم Qt.WA_TranslucentBackground هنا لأنه يسبب مشاكل في الرسم

        # استخدام الجلسة المقدمة أو إنشاء جلسة جديدة
        if session:
            self.session = session
        else:
            # إعداد قاعدة البيانات
            init_db()
            self.session = get_session()

        # تعيين المستخدم الحالي
        self.current_user = current_user

        # إذا لم يتم تمرير المستخدم الحالي، نبحث عن المستخدم الإداري
        if not self.current_user:
            self.current_user = self.session.query(User).filter_by(role="admin").first()

        # تهيئة المتغيرات للتبويبات
        self.dashboard_widget = None

        # متغير لتتبع حالة قائمة الإغلاق
        self.close_dialog_open = False

        # إعداد النافذة الرئيسية
        self.setWindowTitle("👨‍💻 Programmer Engineer | Khaled Mohsen - Smart Finish")
        self.setMinimumSize(1200, 800)

        # إخفاء شريط القوائم بالكامل فوراً
        self.menuBar().setVisible(False)
        self.menuBar().hide()
        self.menuBar().setMaximumHeight(0)
        self.menuBar().setMinimumHeight(0)

        # إعداد شريط الحالة (سيتم إنشاؤه لاحقاً)
        # لا نخفي شريط الحالة هنا لأننا سنحتاجه

        # إخفاء شريط العنوان الأساسي تماماً واستخدام المخصص فقط
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground, False)

        # تعطيل التحديث التلقائي لتجنب الوميض
        self.setUpdatesEnabled(False)

        # تطبيق نمط عام للنافذة الرئيسية فقط (بدون تأثير على العناصر الفرعية)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #cbd5e1;
                border: none;
            }
        """)

        # إنشاء الواجهة الرئيسية
        self.create_main_ui()

        # إضافة اختصارات لوحة المفاتيح
        self.setup_shortcuts()

        # فرض إظهار جميع الأشرطة بعد عرض النافذة
        QTimer.singleShot(50, self.force_toolbars_visibility)

        # التحقق من الإشعارات (تأخير أكبر لتحسين الأداء)
        QTimer.singleShot(20000, self.check_notifications)  # تأخير 20 ثانية لتحسين وقت بدء التشغيل

        # لا نحتاج مؤقت إضافي هنا لأن شريط الحالة المحفوظ له مؤقته الخاص

    def showEvent(self, event):
        """حدث عرض النافذة - لضمان إظهار الأشرطة بعد عرض النافذة"""
        super().showEvent(event)
        # فرض إظهار الأشرطة بعد عرض النافذة مباشرة
        QTimer.singleShot(100, self.final_force_visibility)

    def final_force_visibility(self):
        """فرض إظهار الأشرطة نهائياً بعد عرض النافذة"""
        try:
            if hasattr(self, 'custom_title_bar') and self.custom_title_bar:
                self.custom_title_bar.setVisible(True)
                self.custom_title_bar.show()
                self.custom_title_bar.raise_()
                print(f"🔧 شريط العنوان نهائياً: {self.custom_title_bar.isVisible()}")

            if hasattr(self, 'h_toolbar_widget') and self.h_toolbar_widget:
                self.h_toolbar_widget.setVisible(True)
                self.h_toolbar_widget.show()
                print(f"🔧 الشريط الأفقي نهائياً: {self.h_toolbar_widget.isVisible()}")

        except Exception as e:
            print(f"❌ خطأ في الفرض النهائي: {e}")

    def create_custom_title_bar(self):
        """إنشاء شريط عنوان مخصص متطور مع تصميم احترافي"""
        try:
            print("🔧 بدء إنشاء شريط العنوان المخصص...")

            # إنشاء شريط العنوان المخصص
            self.custom_title_bar = QWidget()
            self.custom_title_bar.setObjectName("customTitleBar")
            self.custom_title_bar.setFixedHeight(28)  # ارتفاع أقل جداً
            self.custom_title_bar.setMinimumHeight(28)
            self.custom_title_bar.setMaximumHeight(28)

            # أخذ العرض الكامل للنافذة
            self.custom_title_bar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            # التأكد من أن العنصر مرئي
            self.custom_title_bar.setVisible(True)
            self.custom_title_bar.show()

            # تطبيق النمط الموحد الكامل للشريط مع أولوية عالية
            self.custom_title_bar.setStyleSheet("""
                QWidget#customTitleBar {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6) !important;
                    border: none !important;
                    padding: 4px 12px !important;
                    margin: 0px !important;
                    min-height: 28px !important;
                    max-height: 28px !important;
                }
                QWidget#customTitleBar QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.3 rgba(255, 255, 255, 0.35),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15)) !important;
                    color: #ffffff !important;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif !important;
                    font-weight: 700 !important;
                    font-size: 11px !important;
                    border: none !important;
                    border-radius: 12px !important;
                    padding: 4px 10px !important;
                }
                QWidget#customTitleBar QLabel#centerTitle {
                    font-size: 16px !important;
                    font-weight: 900 !important;
                    text-align: center !important;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7)) !important;
                    border: none !important;
                    border-radius: 15px !important;
                    transition: all 0.3s ease !important;
                }
                QWidget#customTitleBar QLabel#leftTitle {
                    text-align: center !important;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.2 rgba(255, 255, 255, 0.12),
                        stop:0.4 rgba(255, 255, 255, 0.15),
                        stop:0.6 rgba(255, 255, 255, 0.12),
                        stop:0.8 rgba(255, 255, 255, 0.08),
                        stop:1 rgba(255, 255, 255, 0.05)) !important;
                    border: none !important;
                    border-radius: 15px !important;
                    transition: all 0.3s ease !important;
                }
                QWidget#customTitleBar QLabel#rightTitle {
                    text-align: center !important;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7)) !important;
                    border: none !important;
                    border-radius: 15px !important;
                }

                QWidget#customTitleBar QLabel#centerTitle:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8)) !important;
                    transform: translateY(-1px) !important;
                    box-shadow:
                        0 4px 15px rgba(0, 0, 0, 0.3),
                        0 0 20px rgba(96, 165, 250, 0.4),
                        inset 0 1px 3px rgba(255, 255, 255, 0.3) !important;
                }
                QWidget#customTitleBar QLabel#rightTitle:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8)) !important;
                    transform: translateY(-1px) !important;
                    box-shadow:
                        0 4px 15px rgba(0, 0, 0, 0.3),
                        0 0 20px rgba(96, 165, 250, 0.4),
                        inset 0 1px 3px rgba(255, 255, 255, 0.3) !important;
                }
                QWidget#customTitleBar QLabel#leftTitle:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.20),
                        stop:0.2 rgba(255, 255, 255, 0.25),
                        stop:0.4 rgba(255, 255, 255, 0.30),
                        stop:0.6 rgba(255, 255, 255, 0.25),
                        stop:0.8 rgba(255, 255, 255, 0.20),
                        stop:1 rgba(255, 255, 255, 0.15)) !important;
                    transform: translateY(-2px) scale(1.02) !important;
                    box-shadow:
                        0 6px 20px rgba(0, 0, 0, 0.4),
                        0 0 30px rgba(255, 255, 255, 0.3),
                        inset 0 2px 4px rgba(255, 255, 255, 0.4),
                        inset 0 -1px 2px rgba(0, 0, 0, 0.1) !important;
                    filter: brightness(1.1) !important;
                }
                QWidget#customTitleBar QLabel:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.7),
                        stop:0.3 rgba(255, 255, 255, 0.6),
                        stop:0.7 rgba(255, 255, 255, 0.5),
                        stop:1 rgba(255, 255, 255, 0.4)) !important;
                    border: none !important;
                    border-radius: 18px !important;
                }
                QWidget#customTitleBar QLabel#centerTitle:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(29, 78, 216, 0.9),
                        stop:0.2 rgba(37, 99, 235, 0.8),
                        stop:0.4 rgba(59, 130, 246, 0.7),
                        stop:0.6 rgba(96, 165, 250, 0.8),
                        stop:0.8 rgba(124, 58, 237, 0.9),
                        stop:1 rgba(109, 40, 217, 0.8)) !important;
                    transform: translateY(1px) !important;
                    box-shadow:
                        0 0 15px rgba(96, 165, 250, 0.5),
                        0 2px 8px rgba(0, 0, 0, 0.4),
                        inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
                }
                QWidget#customTitleBar QLabel#rightTitle:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(29, 78, 216, 0.9),
                        stop:0.2 rgba(37, 99, 235, 0.8),
                        stop:0.4 rgba(59, 130, 246, 0.7),
                        stop:0.6 rgba(96, 165, 250, 0.8),
                        stop:0.8 rgba(124, 58, 237, 0.9),
                        stop:1 rgba(109, 40, 217, 0.8)) !important;
                    transform: translateY(1px) !important;
                    box-shadow:
                        0 0 15px rgba(96, 165, 250, 0.5),
                        0 2px 8px rgba(0, 0, 0, 0.4),
                        inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
                }
                QWidget#customTitleBar QLabel#leftTitle:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(29, 78, 216, 0.9),
                        stop:0.2 rgba(37, 99, 235, 0.8),
                        stop:0.4 rgba(59, 130, 246, 0.7),
                        stop:0.6 rgba(96, 165, 250, 0.8),
                        stop:0.8 rgba(124, 58, 237, 0.9),
                        stop:1 rgba(109, 40, 217, 0.8)) !important;
                    transform: translateY(1px) !important;
                    box-shadow:
                        0 0 15px rgba(96, 165, 250, 0.5),
                        0 2px 8px rgba(0, 0, 0, 0.4),
                        inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
                }
                QWidget#customTitleBar QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.5),
                        stop:0.3 rgba(255, 255, 255, 0.4),
                        stop:0.7 rgba(255, 255, 255, 0.3),
                        stop:1 rgba(255, 255, 255, 0.2)) !important;
                    border: none !important;
                    border-radius: 20px !important;
                    color: #ffffff !important;
                    font-weight: 900 !important;
                    font-size: 12px !important;
                    min-width: 30px !important;
                    max-width: 30px !important;
                    min-height: 30px !important;
                    max-height: 30px !important;
                    padding: 0px !important;
                }
                QWidget#customTitleBar QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.3 rgba(255, 255, 255, 0.8),
                        stop:0.7 rgba(255, 255, 255, 0.7),
                        stop:1 rgba(255, 255, 255, 0.6)) !important;
                    border: none !important;
                    border-radius: 25px !important;
                    transform: translateY(-1px) !important;
                    box-shadow:
                        0 3px 10px rgba(0, 0, 0, 0.2),
                        0 0 15px rgba(255, 255, 255, 0.3),
                        inset 0 1px 2px rgba(255, 255, 255, 0.4) !important;
                }
                QWidget#customTitleBar QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(200, 200, 200, 0.8),
                        stop:0.3 rgba(220, 220, 220, 0.7),
                        stop:0.7 rgba(240, 240, 240, 0.6),
                        stop:1 rgba(255, 255, 255, 0.5)) !important;
                    border: none !important;
                    border-radius: 25px !important;
                    transform: translateY(1px) !important;
                    box-shadow:
                        0 1px 5px rgba(0, 0, 0, 0.3),
                        inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
                }
                QWidget#customTitleBar QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.3 rgba(255, 255, 255, 0.8),
                        stop:0.7 rgba(255, 255, 255, 0.7),
                        stop:1 rgba(255, 255, 255, 0.6)) !important;
                    border: none !important;
                    border-radius: 22px !important;
                }
                QWidget#customTitleBar QPushButton#closeBtn {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 99, 71, 0.9),
                        stop:0.3 rgba(231, 76, 60, 0.8),
                        stop:0.7 rgba(192, 57, 43, 0.7),
                        stop:1 rgba(220, 20, 60, 0.6)) !important;
                    border: none !important;
                }
                QWidget#customTitleBar QPushButton#closeBtn:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 69, 0, 1.0),
                        stop:0.3 rgba(255, 99, 71, 0.9),
                        stop:0.7 rgba(231, 76, 60, 0.8),
                        stop:1 rgba(255, 20, 147, 0.7)) !important;
                    border: none !important;
                    border-radius: 25px !important;
                }
                QWidget#customTitleBar QPushButton#closeBtn:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(220, 20, 60, 1.0),
                        stop:0.3 rgba(192, 57, 43, 0.9),
                        stop:0.7 rgba(139, 69, 19, 0.8),
                        stop:1 rgba(128, 0, 128, 0.7)) !important;
                    border: none !important;
                    border-radius: 22px !important;
                }
            """)

            # إنشاء التخطيط الأفقي للشريط
            title_layout = QHBoxLayout(self.custom_title_bar)
            title_layout.setContentsMargins(5, 1, 5, 1)
            title_layout.setSpacing(6)

            # إضافة أزرار التحكم في النافذة (تصغير، تكبير، إغلاق) - في أقصى اليسار
            self.create_window_control_buttons(title_layout)

            # مساحة كبيرة لدفع الأزرار إلى اليسار
            title_layout.addSpacing(30)

            # إضافة معلومات المستخدم (بدون الوقت)
            self.create_title_info_section(title_layout)

            # مساحة مرنة
            title_layout.addStretch()

            # العنوان المركزي - اسم الشركة
            self.center_title_label = QLabel("🏢 Smart Finish")
            self.center_title_label.setObjectName("centerTitle")
            self.center_title_label.setAlignment(Qt.AlignCenter)
            self.center_title_label.setMinimumWidth(200)  # التراجع للحجم الأصلي
            # إزالة الحد الأقصى للعرض
            self.center_title_label.setToolTip("شركة Smart Finish - حلول محاسبية متطورة")
            title_layout.addWidget(self.center_title_label)

            # مساحة مرنة
            title_layout.addStretch()

            # العنوان الأيمن - اسم المطور (زيادة العرض 4 درجات + فاصل)
            self.right_title_label = QLabel("👨‍💻 Programmer Engineer | Khaled Mohsen")
            self.right_title_label.setObjectName("rightTitle")
            self.right_title_label.setAlignment(Qt.AlignCenter | Qt.AlignVCenter)  # محاذاة في المنتصف
            self.right_title_label.setMinimumWidth(260)  # زيادة العرض الأدنى درجتين إضافيتين (من 240 إلى 260)
            self.right_title_label.setMaximumWidth(280)  # زيادة الحد الأقصى درجتين إضافيتين (من 260 إلى 280)
            self.right_title_label.setToolTip("المطور والمهندس المسؤول عن البرنامج")
            title_layout.addWidget(self.right_title_label)

            # إضافة وظائف السحب للنافذة
            self.setup_window_dragging()

            print("✅ تم إنشاء شريط العنوان المخصص بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء شريط العنوان المخصص: {e}")

    def create_title_toolbar_separator(self):
        """إنشاء فاصل رفيع وأنيق بين شريط العنوان والشريط الأفقي"""
        try:
            # إنشاء الفاصل
            self.title_toolbar_separator = QWidget()
            self.title_toolbar_separator.setObjectName("titleToolbarSeparator")
            self.title_toolbar_separator.setFixedHeight(3)  # ارتفاع رفيع جداً
            self.title_toolbar_separator.setMinimumHeight(3)
            self.title_toolbar_separator.setMaximumHeight(3)

            # أخذ العرض الكامل للنافذة
            self.title_toolbar_separator.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            # تطبيق نمط متطور وأنيق للفاصل
            self.title_toolbar_separator.setStyleSheet("""
                QWidget#titleToolbarSeparator {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(15, 23, 42, 0.3),
                        stop:0.1 rgba(37, 99, 235, 0.6),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.3 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.9),
                        stop:0.5 rgba(167, 139, 250, 1.0),
                        stop:0.6 rgba(124, 58, 237, 0.9),
                        stop:0.7 rgba(5, 150, 105, 0.8),
                        stop:0.8 rgba(16, 185, 129, 0.7),
                        stop:0.9 rgba(52, 211, 153, 0.6),
                        stop:1 rgba(91, 33, 182, 0.5)) !important;
                    border: none !important;
                    margin: 0px !important;
                    padding: 0px !important;
                }
            """)

            # التأكد من أن الفاصل مرئي
            self.title_toolbar_separator.setVisible(True)
            self.title_toolbar_separator.show()

            print("✅ تم إنشاء الفاصل الأنيق بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء الفاصل الأنيق: {e}")

    def setup_window_dragging(self):
        """إعداد وظيفة سحب النافذة من شريط العنوان"""
        try:
            # متغيرات لتتبع السحب
            self.dragging = False
            self.drag_position = None

            # تطبيق أحداث الماوس على شريط العنوان
            self.custom_title_bar.mousePressEvent = self.title_bar_mouse_press
            self.custom_title_bar.mouseMoveEvent = self.title_bar_mouse_move
            self.custom_title_bar.mouseReleaseEvent = self.title_bar_mouse_release
            self.custom_title_bar.mouseDoubleClickEvent = self.title_bar_double_click

            print("✅ تم إعداد وظيفة سحب النافذة")

        except Exception as e:
            print(f"❌ خطأ في إعداد وظيفة السحب: {e}")

    def title_bar_mouse_press(self, event):
        """بداية سحب النافذة - مع منع السحب عند التكبير أو فتح قائمة الإغلاق"""
        try:
            if (event.button() == Qt.LeftButton and
                not self.close_dialog_open and
                not self.isMaximized()):
                self.dragging = True
                self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
                event.accept()
        except Exception as e:
            print(f"❌ خطأ في بداية السحب: {e}")

    def title_bar_mouse_move(self, event):
        """تحريك النافذة أثناء السحب - مع منع السحب عند التكبير أو فتح قائمة الإغلاق"""
        try:
            if (event.buttons() == Qt.LeftButton and self.dragging and
                self.drag_position and not self.close_dialog_open and
                not self.isMaximized()):
                self.move(event.globalPos() - self.drag_position)
                event.accept()
        except Exception as e:
            print(f"❌ خطأ في تحريك النافذة: {e}")

    def title_bar_mouse_release(self, event):
        """انتهاء سحب النافذة"""
        try:
            if event.button() == Qt.LeftButton:
                self.dragging = False
                self.drag_position = None
                event.accept()
        except Exception as e:
            print(f"❌ خطأ في انتهاء السحب: {e}")

    def title_bar_double_click(self, event):
        """النقر المزدوج لتكبير/تصغير النافذة"""
        try:
            if event.button() == Qt.LeftButton:
                self.toggle_maximize()
                event.accept()
        except Exception as e:
            print(f"❌ خطأ في النقر المزدوج: {e}")

    def create_title_info_section(self, layout):
        """إنشاء قسم المعلومات الإضافية في شريط العنوان"""
        try:
            # حاوية المعلومات
            info_widget = QWidget()
            info_layout = QVBoxLayout(info_widget)
            info_layout.setContentsMargins(5, 2, 5, 2)
            info_layout.setSpacing(2)



            # معلومات المستخدم - سيستخدم النمط الموحد
            self.title_user_label = QLabel()
            self.title_user_label.setObjectName("leftTitle")  # إضافة ObjectName للتوحيد
            self.title_user_label.setAlignment(Qt.AlignCenter)
            self.title_user_label.setMinimumWidth(100)  # عرض أصغر
            self.title_user_label.setMaximumWidth(120)  # حد أقصى للعرض
            info_layout.addWidget(self.title_user_label)

            # تحديث معلومات المستخدم مرة واحدة
            self.update_title_info()

            # إضافة الحاوية للتخطيط
            layout.addWidget(info_widget)

            print("✅ تم إنشاء قسم المعلومات الإضافية في شريط العنوان")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قسم المعلومات: {e}")

    def update_title_info(self):
        """تحديث معلومات المستخدم في شريط العنوان"""
        try:
            # تحديث معلومات المستخدم في سطر واحد (جانب بعض)
            if hasattr(self, 'current_user') and self.current_user:
                # إزالة كلمة admin وعرض دور مبسط
                if self.current_user.role == "admin":
                    role_text = "مدير"
                else:
                    role_text = self.current_user.role
                # عرض اسم المستخدم والدور في سطر واحد
                combined_text = f"👤 {self.current_user.username} | 🔑 {role_text}"
                self.title_user_label.setText(combined_text)
                self.title_user_label.setToolTip(f"المستخدم: {self.current_user.username}\nالدور: {role_text}")
            else:
                self.title_user_label.setText("👤 مستخدم | 🔑 عام")
                self.title_user_label.setToolTip("مستخدم عام")

        except Exception as e:
            print(f"❌ خطأ في تحديث معلومات شريط العنوان: {e}")

    def create_window_control_buttons(self, layout):
        """إنشاء أزرار التحكم في النافذة (تصغير، تكبير، إغلاق) في منتصف الارتفاع"""
        try:
            # حاوية أزرار التحكم مع محاذاة عمودية في المنتصف
            controls_widget = QWidget()
            controls_widget.setFixedHeight(28)  # نفس ارتفاع شريط العنوان
            controls_layout = QHBoxLayout(controls_widget)
            controls_layout.setContentsMargins(5, 0, 5, 0)  # إزالة الهوامش العمودية
            controls_layout.setSpacing(8)

            # محاذاة الأزرار في منتصف الارتفاع
            controls_layout.setAlignment(Qt.AlignVCenter)

            # زر الإغلاق - مع معرف خاص للنمط المختلف (أول زر)
            self.close_btn = QPushButton("✕")
            self.close_btn.setObjectName("closeBtn")
            self.close_btn.setToolTip("إغلاق البرنامج")
            self.close_btn.clicked.connect(self.close_application)
            controls_layout.addWidget(self.close_btn, 0, Qt.AlignVCenter)

            # زر التكبير/الاستعادة - بدون نمط منفصل (ثاني زر)
            self.maximize_btn = QPushButton("🗖")
            self.maximize_btn.setToolTip("تكبير/استعادة النافذة")
            self.maximize_btn.clicked.connect(self.toggle_maximize)
            controls_layout.addWidget(self.maximize_btn, 0, Qt.AlignVCenter)

            # زر التصغير - بدون نمط منفصل (ثالث زر)
            self.minimize_btn = QPushButton("🗕")
            self.minimize_btn.setToolTip("تصغير النافذة")
            self.minimize_btn.clicked.connect(self.showMinimized)
            controls_layout.addWidget(self.minimize_btn, 0, Qt.AlignVCenter)

            # إضافة الحاوية للتخطيط مع محاذاة عمودية في المنتصف
            layout.addWidget(controls_widget, 0, Qt.AlignVCenter)

            print("✅ تم إنشاء أزرار التحكم في النافذة بنجاح - في منتصف الارتفاع")

        except Exception as e:
            print(f"❌ خطأ في إنشاء أزرار التحكم: {e}")

    def toggle_maximize(self):
        """تبديل حالة تكبير النافذة مع تحديث مؤشر السحب"""
        try:
            if self.isMaximized():
                self.showNormal()
                self.maximize_btn.setText("🗖")
                self.maximize_btn.setToolTip("تكبير النافذة")
                # تحديث مؤشر السحب - السماح بالسحب
                self.update_drag_cursor()
            else:
                self.showMaximized()
                self.maximize_btn.setText("🗗")
                self.maximize_btn.setToolTip("استعادة النافذة")
                # تحديث مؤشر السحب - منع السحب
                self.update_drag_cursor()
        except Exception as e:
            print(f"❌ خطأ في تبديل حالة التكبير: {e}")

    def update_drag_cursor(self):
        """تحديث مؤشر السحب حسب حالة النافذة"""
        try:
            if hasattr(self, 'custom_title_bar') and self.custom_title_bar:
                if self.isMaximized() or self.close_dialog_open:
                    # منع السحب - مؤشر عادي
                    self.custom_title_bar.setCursor(Qt.ArrowCursor)
                    self.custom_title_bar.setToolTip("النافذة مكبرة - لا يمكن السحب")
                else:
                    # السماح بالسحب - مؤشر يد
                    self.custom_title_bar.setCursor(Qt.OpenHandCursor)
                    self.custom_title_bar.setToolTip("اسحب لتحريك النافذة")
        except Exception as e:
            print(f"❌ خطأ في تحديث مؤشر السحب: {e}")

    def close_application(self):
        """إغلاق البرنامج مع قائمة تأكيد متطورة"""
        try:
            # التحقق من تفضيلات المستخدم المحفوظة
            if self.check_auto_close_preference():
                print("🔄 إغلاق تلقائي بناءً على تفضيل المستخدم...")
                self.save_data_before_close()
                self.close()
            else:
                # إنشاء قائمة تأكيد مخصصة ومتطورة
                self.create_advanced_close_dialog()
        except Exception as e:
            print(f"❌ خطأ في إغلاق البرنامج: {e}")
            self.close()  # إغلاق اضطراري

    def check_auto_close_preference(self):
        """التحقق من تفضيل الإغلاق التلقائي"""
        try:
            import os
            import json

            # مسار ملف التفضيلات
            preferences_file = "user_preferences.json"

            if os.path.exists(preferences_file):
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    preferences = json.load(f)
                    return preferences.get('auto_close', False)
            return False
        except Exception as e:
            print(f"❌ خطأ في قراءة تفضيلات المستخدم: {e}")
            return False

    def save_user_preference(self, auto_close=False, save_data=True):
        """حفظ تفضيلات المستخدم"""
        try:
            import json
            from datetime import datetime

            preferences = {
                'auto_close': auto_close,
                'save_data': save_data,
                'last_updated': str(datetime.now())
            }

            with open("user_preferences.json", 'w', encoding='utf-8') as f:
                json.dump(preferences, f, ensure_ascii=False, indent=2)

            print(f"💾 تم حفظ تفضيلات المستخدم: إغلاق تلقائي={auto_close}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ تفضيلات المستخدم: {e}")
            return False

    def reset_close_preferences(self):
        """إعادة تعيين تفضيلات الإغلاق (لإظهار القائمة مرة أخرى)"""
        try:
            import os

            preferences_file = "user_preferences.json"
            if os.path.exists(preferences_file):
                os.remove(preferences_file)
                print("🔄 تم إعادة تعيين تفضيلات الإغلاق - ستظهر قائمة التأكيد مرة أخرى")
                return True
            return False
        except Exception as e:
            print(f"❌ خطأ في إعادة تعيين التفضيلات: {e}")
            return False

    def create_advanced_close_dialog(self):
        """إنشاء قائمة إغلاق متطورة وجميلة مع تأثيرات بصرية متقدمة"""
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                                       QLabel, QPushButton, QFrame, QCheckBox,
                                       QProgressBar, QSpacerItem, QSizePolicy,
                                       QGraphicsDropShadowEffect)
            from PyQt5.QtCore import QTimer
            from PyQt5.QtGui import QFont, QColor

            # تعيين حالة القائمة كمفتوحة
            self.close_dialog_open = True
            # تحديث مؤشر السحب
            self.update_drag_cursor()

            # إنشاء النافذة المخصصة مع حجم مرن
            dialog = QDialog(self)
            dialog.setWindowTitle("Smart Finish - إغلاق البرنامج")
            dialog.setMinimumSize(480, 450)  # حد أدنى مرن
            dialog.setMaximumSize(500, 480)  # حد أقصى مرن
            dialog.resize(480, 450)  # حجم افتراضي
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
            dialog.setAttribute(Qt.WA_TranslucentBackground)

            # إضافة خاصية السحب للنافذة
            dialog.mousePressEvent = lambda event: self.start_drag_dialog(event, dialog)
            dialog.mouseMoveEvent = lambda event: self.drag_dialog(event, dialog)
            dialog.mouseReleaseEvent = lambda event: self.end_drag_dialog(event, dialog)

            # إضافة تأثير الظل للنافذة
            shadow_effect = QGraphicsDropShadowEffect()
            shadow_effect.setBlurRadius(25)
            shadow_effect.setColor(QColor(0, 0, 0, 100))
            shadow_effect.setOffset(0, 10)
            dialog.setGraphicsEffect(shadow_effect)

            # تطبيق نمط متطور ومتقدم للنافذة
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.15 #764ba2, stop:0.3 #f093fb,
                        stop:0.45 #f5576c, stop:0.6 #4facfe, stop:0.75 #00f2fe,
                        stop:0.9 #43e97b, stop:1 #38f9d7);
                    border-radius: 20px;
                    border: 3px solid #ffffff;
                }
                QLabel {
                    color: white;
                    background: transparent;
                }
                QLabel#titleLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3498db, stop:1 #2980b9);
                    border-radius: 12px;
                    padding: 12px;
                    border: 2px solid #ffffff;
                }
                QLabel#messageLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #e74c3c, stop:1 #c0392b);
                    border-radius: 10px;
                    padding: 10px;
                    border: 2px solid #ffffff;
                }
                QLabel#infoLabel {
                    font-size: 12px;
                    color: white;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #34495e, stop:1 #2c3e50);
                    border-radius: 8px;
                    padding: 10px;
                    border: 1px solid #ffffff;
                }
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #95a5a6, stop:1 #7f8c8d);
                    border: 2px solid #ffffff;
                    border-radius: 15px;
                    color: white;
                    font-weight: bold;
                    font-size: 12px;
                    padding: 12px 8px;
                    min-width: 110px;
                    max-width: 130px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #bdc3c7, stop:1 #95a5a6);
                    border: 3px solid #ffffff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #7f8c8d, stop:1 #95a5a6);
                    border: 2px solid #ecf0f1;
                }
                QPushButton#closeBtn {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #e74c3c, stop:1 #c0392b);
                    border: 3px solid #ffffff;
                    min-width: 115px;
                    max-width: 135px;
                }
                QPushButton#closeBtn:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ff6b6b, stop:1 #e74c3c);
                    border: 3px solid #ffffff;
                }
                QPushButton#closeBtn:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #c0392b, stop:1 #a93226);
                }
                QPushButton#minimizeBtn {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3498db, stop:1 #2980b9);
                    border: 3px solid #ffffff;
                    min-width: 115px;
                    max-width: 135px;
                }
                QPushButton#minimizeBtn:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5dade2, stop:1 #3498db);
                    border: 3px solid #ffffff;
                }
                QPushButton#minimizeBtn:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2980b9, stop:1 #21618c);
                }
                QPushButton#cancelBtn {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #95a5a6, stop:1 #7f8c8d);
                    border: 3px solid #ffffff;
                    min-width: 115px;
                    max-width: 135px;
                }
                QPushButton#cancelBtn:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #bdc3c7, stop:1 #95a5a6);
                    border: 3px solid #ffffff;
                }
                QPushButton#cancelBtn:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #7f8c8d, stop:1 #566573);
                }
                QCheckBox {
                    color: white;
                    font-size: 12px;
                    font-weight: bold;
                    spacing: 10px;
                }
                QCheckBox::indicator {
                    width: 22px;
                    height: 22px;
                    border: 3px solid #ffffff;
                    border-radius: 8px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ecf0f1, stop:1 #bdc3c7);
                }
                QCheckBox::indicator:hover {
                    border: 3px solid #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffffff, stop:1 #ecf0f1);
                }
                QCheckBox::indicator:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2ecc71, stop:1 #27ae60);
                    border: 3px solid #ffffff;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjUgNEw2IDExLjVMMi41IDgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
                }
                QProgressBar {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #34495e, stop:1 #2c3e50);
                    border: 3px solid #ffffff;
                    border-radius: 15px;
                    text-align: center;
                    color: white;
                    font-weight: bold;
                    font-size: 12px;
                    min-height: 28px;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #43e97b, stop:0.25 #38f9d7, stop:0.5 #4facfe,
                        stop:0.75 #667eea, stop:1 #f093fb);
                    border-radius: 12px;
                    margin: 3px;
                }
                QFrame#separator {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #ffffff, stop:0.5 #ecf0f1, stop:1 #ffffff);
                    height: 4px;
                    border: none;
                    border-radius: 2px;
                    margin: 5px 0px;
                }
            """)

            # التخطيط الرئيسي مع تباعد محسن
            main_layout = QVBoxLayout(dialog)
            main_layout.setContentsMargins(18, 18, 18, 18)
            main_layout.setSpacing(12)

            # العنوان الرئيسي مع تصميم متطور
            title_label = QLabel("🚪 إغلاق برنامج Smart Finish")
            title_label.setObjectName("titleLabel")
            title_font = QFont("Arial", 15, QFont.Bold)
            title_label.setFont(title_font)
            title_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(title_label)

            # فاصل جميل ومتطور
            separator = QFrame()
            separator.setObjectName("separator")
            separator.setFrameShape(QFrame.HLine)
            separator.setFixedHeight(3)
            main_layout.addWidget(separator)

            # رسالة التأكيد مع تصميم محسن
            message_label = QLabel("⚠️ هل أنت متأكد من إغلاق البرنامج؟")
            message_label.setObjectName("messageLabel")
            message_font = QFont("Arial", 13, QFont.Bold)
            message_label.setFont(message_font)
            message_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(message_label)

            # معلومات إضافية مع تصميم متطور وحجم أكبر
            info_label = QLabel("💾 سيتم حفظ جميع البيانات والتغييرات تلقائياً\n🔒 سيتم إغلاق جميع الاتصالات والعمليات بأمان\n📁 سيتم حفظ حالة النوافذ والإعدادات الحالية")
            info_label.setObjectName("infoLabel")
            info_font = QFont("Arial", 11)
            info_label.setFont(info_font)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setWordWrap(True)
            info_label.setMinimumHeight(70)  # ارتفاع محسن لعرض النص كاملاً
            main_layout.addWidget(info_label)

            # خيارات إضافية مع تصميم محسن
            options_layout = QVBoxLayout()
            options_layout.setSpacing(8)

            # خيار حفظ البيانات مع وصف تفصيلي
            save_checkbox = QCheckBox("💾 حفظ البيانات قبل الإغلاق (موصى به)")
            save_checkbox.setChecked(True)
            save_checkbox.setToolTip("سيتم حفظ جميع البيانات والتغييرات الحالية قبل إغلاق البرنامج")
            options_layout.addWidget(save_checkbox)

            # خيار تذكر الاختيار مع وصف
            remember_checkbox = QCheckBox("🧠 تذكر اختياري (عدم إظهار هذه الرسالة مرة أخرى)")
            remember_checkbox.setToolTip("سيتم حفظ تفضيلك وإغلاق البرنامج مباشرة في المرات القادمة")
            options_layout.addWidget(remember_checkbox)

            main_layout.addLayout(options_layout)

            # شريط التقدم المتطور (مخفي في البداية)
            progress_bar = QProgressBar()
            progress_bar.setVisible(False)
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            progress_bar.setFormat("جاري التحضير...")
            main_layout.addWidget(progress_bar)

            # مساحة مرنة
            main_layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Expanding))

            # أزرار التحكم مع تصميم محسن
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(10)

            # زر الإلغاء مع تصميم مميز
            cancel_btn = QPushButton("❌ إلغاء والعودة")
            cancel_btn.setObjectName("cancelBtn")
            cancel_btn.setToolTip("العودة إلى البرنامج بدون إغلاق")
            cancel_btn.clicked.connect(dialog.reject)
            buttons_layout.addWidget(cancel_btn)

            # زر التصغير مع وصف
            minimize_btn = QPushButton("🗕 تصغير فقط")
            minimize_btn.setObjectName("minimizeBtn")
            minimize_btn.setToolTip("تصغير النافذة بدلاً من الإغلاق")
            minimize_btn.clicked.connect(lambda: self.minimize_and_close_dialog(dialog))
            buttons_layout.addWidget(minimize_btn)

            # زر الإغلاق الرئيسي
            close_btn = QPushButton("🚪 إغلاق البرنامج")
            close_btn.setObjectName("closeBtn")
            close_btn.setToolTip("إغلاق البرنامج نهائياً مع حفظ البيانات")
            close_btn.clicked.connect(lambda: self.confirm_close(dialog, save_checkbox.isChecked(),
                                                               remember_checkbox.isChecked(),
                                                               False, progress_bar))
            buttons_layout.addWidget(close_btn)

            main_layout.addLayout(buttons_layout)

            # تشغيل النافذة
            result = dialog.exec_()

            # إعادة تعيين حالة القائمة عند الإغلاق
            self.close_dialog_open = False
            # تحديث مؤشر السحب
            self.update_drag_cursor()

        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة الإغلاق المتطورة: {e}")
            # العودة للقائمة البسيطة في حالة الخطأ
            self.simple_close_dialog()

    def minimize_and_close_dialog(self, dialog):
        """تصغير النافذة وإغلاق القائمة مع السماح بالسحب"""
        try:
            # السماح بالسحب مرة أخرى
            self.close_dialog_open = False
            # تحديث مؤشر السحب
            self.update_drag_cursor()
            self.showMinimized()
            dialog.accept()
        except Exception as e:
            print(f"❌ خطأ في تصغير النافذة: {e}")

    def start_drag_dialog(self, event, dialog):
        """بداية سحب نافذة الحوار"""
        try:
            if event.button() == Qt.LeftButton:
                dialog.drag_position = event.globalPos() - dialog.frameGeometry().topLeft()
                event.accept()
        except Exception as e:
            print(f"❌ خطأ في بداية سحب النافذة: {e}")

    def drag_dialog(self, event, dialog):
        """سحب نافذة الحوار"""
        try:
            if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_position'):
                dialog.move(event.globalPos() - dialog.drag_position)
                event.accept()
        except Exception as e:
            print(f"❌ خطأ في سحب النافذة: {e}")

    def end_drag_dialog(self, event, dialog):
        """انتهاء سحب نافذة الحوار"""
        try:
            if event.button() == Qt.LeftButton:
                if hasattr(dialog, 'drag_position'):
                    delattr(dialog, 'drag_position')
                event.accept()
        except Exception as e:
            print(f"❌ خطأ في انتهاء سحب النافذة: {e}")

    def confirm_close(self, dialog, save_data, remember_choice, create_backup, progress_bar):
        """تأكيد الإغلاق مع شريط التقدم المتطور"""
        try:
            from PyQt5.QtCore import QTimer

            # إظهار شريط التقدم
            progress_bar.setVisible(True)
            progress_bar.setValue(0)

            # محاكاة عملية الحفظ والإغلاق المتطورة
            timer = QTimer()
            progress = [0]  # استخدام قائمة للتمكن من التعديل داخل الدالة

            def update_progress():
                progress[0] += 10
                progress_bar.setValue(progress[0])

                if progress[0] == 20:
                    progress_bar.setFormat("💾 جاري حفظ البيانات...")
                elif progress[0] == 40:
                    progress_bar.setFormat("📊 حفظ التقارير والإحصائيات...")
                elif progress[0] == 60:
                    progress_bar.setFormat("🔒 إغلاق اتصالات قاعدة البيانات...")
                elif progress[0] == 80:
                    progress_bar.setFormat("🌐 إغلاق الاتصالات الشبكية...")
                elif progress[0] == 90:
                    progress_bar.setFormat("🚪 إنهاء العمليات وإغلاق البرنامج...")
                elif progress[0] >= 100:
                    progress_bar.setFormat("✅ تم الإغلاق بنجاح!")
                    timer.stop()

                    # تأخير صغير لإظهار رسالة النجاح
                    QTimer.singleShot(500, lambda: self.finalize_close(dialog, save_data, remember_choice, create_backup))

            timer.timeout.connect(update_progress)
            timer.start(180)  # تحديث كل 180ms

        except Exception as e:
            print(f"❌ خطأ في تأكيد الإغلاق: {e}")
            dialog.accept()
            self.close()

    def finalize_close(self, dialog, save_data, remember_choice, create_backup):
        """إنهاء عملية الإغلاق مع حفظ التفضيلات"""
        try:
            dialog.accept()

            # حفظ البيانات إذا كان مطلوباً
            if save_data:
                print("💾 حفظ البيانات...")
                self.save_data_before_close()

            # إنشاء نسخة احتياطية إذا كان مطلوباً
            if create_backup:
                print("🗄️ إنشاء نسخة احتياطية...")
                # يمكن إضافة كود النسخ الاحتياطي هنا

            # حفظ تفضيل المستخدم إذا كان مطلوباً
            if remember_choice:
                print("💾 حفظ تفضيل الإغلاق التلقائي...")
                self.save_user_preference(auto_close=True, save_data=save_data)
                print("✅ سيتم إغلاق البرنامج تلقائياً في المرات القادمة")

            print("🔄 جاري إغلاق البرنامج...")
            self.close()

        except Exception as e:
            print(f"❌ خطأ في إنهاء الإغلاق: {e}")
            self.close()

    def simple_close_dialog(self):
        """قائمة إغلاق بسيطة كبديل"""
        try:
            # تعيين حالة القائمة كمفتوحة
            self.close_dialog_open = True
            # تحديث مؤشر السحب
            self.update_drag_cursor()

            reply = QMessageBox.question(
                self,
                "تأكيد الإغلاق",
                "هل أنت متأكد من إغلاق البرنامج؟\n\nسيتم حفظ جميع البيانات تلقائياً.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            # إعادة تعيين حالة القائمة
            self.close_dialog_open = False
            # تحديث مؤشر السحب
            self.update_drag_cursor()

            if reply == QMessageBox.Yes:
                print("🔄 جاري إغلاق البرنامج...")
                self.save_data_before_close()
                self.close()
        except Exception as e:
            print(f"❌ خطأ في القائمة البسيطة: {e}")
            self.close_dialog_open = False
            # تحديث مؤشر السحب
            self.update_drag_cursor()
            self.close()

    def save_data_before_close(self):
        """حفظ البيانات قبل إغلاق البرنامج"""
        try:
            # حفظ بيانات الجلسة
            if hasattr(self, 'session') and self.session:
                self.session.commit()
                print("✅ تم حفظ بيانات الجلسة")

            # حفظ إعدادات النافذة
            print("✅ تم حفظ إعدادات البرنامج")

        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")



    def force_toolbars_visibility(self):
        """فرض إظهار جميع الأشرطة وإخفاء القوائم غير المرغوبة"""
        try:
            print("🔧 فرض إظهار جميع الأشرطة...")

            # التأكد من إخفاء القوائم الافتراضية فقط (ليس شريط الحالة)
            self.menuBar().setVisible(False)
            self.menuBar().hide()
            self.menuBar().setMaximumHeight(0)
            self.menuBar().setMinimumHeight(0)

            # لا نخفي شريط الحالة لأننا نحتاجه للشريط المخصص

            # إزالة أي أشرطة أدوات قديمة
            all_toolbars = self.findChildren(QToolBar)
            for toolbar in all_toolbars:
                if toolbar.objectName() not in ["customTitleToolbar"]:
                    toolbar.setVisible(False)
                    toolbar.hide()
                    self.removeToolBar(toolbar)

            # فرض إظهار شريط العنوان المخصص
            if hasattr(self, 'custom_title_bar') and self.custom_title_bar:
                self.custom_title_bar.setVisible(True)
                self.custom_title_bar.show()
                self.custom_title_bar.raise_()
                self.custom_title_bar.update()
                self.custom_title_bar.repaint()
                print(f"📊 شريط العنوان بعد الفرض: {self.custom_title_bar.isVisible()}")
                print(f"📊 أبعاد شريط العنوان: العرض={self.custom_title_bar.width()}, الارتفاع={self.custom_title_bar.height()}")

            # فرض إظهار الشريط الأفقي الجديد
            if hasattr(self, 'h_toolbar_widget') and self.h_toolbar_widget:
                self.h_toolbar_widget.setVisible(True)
                self.h_toolbar_widget.show()
                print(f"📊 الشريط الأفقي بعد الفرض: {self.h_toolbar_widget.isVisible()}")

            # فرض التحديث العام
            self.update()
            self.repaint()

            print("✅ تم فرض إظهار جميع الأشرطة وإخفاء القوائم")

        except Exception as e:
            print(f"❌ خطأ في فرض إظهار الأشرطة: {e}")

    def check_permission(self, permission_name):
        """التحقق من صلاحية المستخدم الحالي"""
        if not self.current_user:
            return False

        # المدير لديه جميع الصلاحيات
        if self.current_user.role == "admin":
            return True

        # التحقق من الصلاحية المحددة
        if hasattr(self.current_user, permission_name):
            return getattr(self.current_user, permission_name)

        return False

    def setup_shortcuts(self):
        """إضافة اختصارات لوحة المفاتيح للشريط الأفقي"""
        # F1 - لوحة المعلومات
        dashboard_shortcut = QShortcut(QKeySequence("F1"), self)
        dashboard_shortcut.activated.connect(lambda: self.activate_toolbar_button(0))

        # F2 - العملاء
        clients_shortcut = QShortcut(QKeySequence("F2"), self)
        clients_shortcut.activated.connect(lambda: self.activate_toolbar_button(1))

        # F3 - الموردين
        suppliers_shortcut = QShortcut(QKeySequence("F3"), self)
        suppliers_shortcut.activated.connect(lambda: self.activate_toolbar_button(2))

        # F4 - الموظفين
        employees_shortcut = QShortcut(QKeySequence("F4"), self)
        employees_shortcut.activated.connect(lambda: self.activate_toolbar_button(3))

        # F5 - المشاريع
        projects_shortcut = QShortcut(QKeySequence("F5"), self)
        projects_shortcut.activated.connect(lambda: self.activate_toolbar_button(4))

        # F6 - المخازن
        inventory_shortcut = QShortcut(QKeySequence("F6"), self)
        inventory_shortcut.activated.connect(lambda: self.activate_toolbar_button(5))

        # F7 - المصروفات
        expenses_shortcut = QShortcut(QKeySequence("F7"), self)
        expenses_shortcut.activated.connect(lambda: self.activate_toolbar_button(6))

        # F8 - الإيرادات
        revenues_shortcut = QShortcut(QKeySequence("F8"), self)
        revenues_shortcut.activated.connect(lambda: self.activate_toolbar_button(7))

        # F9 - الفواتير
        invoices_shortcut = QShortcut(QKeySequence("F9"), self)
        invoices_shortcut.activated.connect(lambda: self.activate_toolbar_button(8))

        # F10 - الإشعارات
        notifications_shortcut = QShortcut(QKeySequence("F10"), self)
        notifications_shortcut.activated.connect(lambda: self.activate_toolbar_button(9))

        # F11 - التقارير
        reports_shortcut = QShortcut(QKeySequence("F11"), self)
        reports_shortcut.activated.connect(lambda: self.activate_toolbar_button(10))

        # F12 - الإعدادات
        settings_shortcut = QShortcut(QKeySequence("F12"), self)
        settings_shortcut.activated.connect(lambda: self.activate_toolbar_button(11))

        # اختصار للخروج من البرنامج
        exit_shortcut = QShortcut(QKeySequence("Alt+F4"), self)
        exit_shortcut.activated.connect(self.close)

        print("✅ تم إعداد اختصارات لوحة المفاتيح للشريط الأفقي")

    def activate_toolbar_button(self, button_index):
        """تفعيل زر الشريط الأفقي بالفهرس المحدد"""
        try:
            # التحقق من وجود أزرار الشريط الأفقي الجديد
            if hasattr(self, 'h_toolbar_buttons') and len(self.h_toolbar_buttons) > button_index:
                # تفعيل الزر المحدد
                button = self.h_toolbar_buttons[button_index]
                button.click()
                print(f"✅ تم تفعيل زر الشريط الأفقي: {button_index}")
            else:
                print(f"❌ لا يمكن العثور على زر الشريط الأفقي: {button_index}")
        except Exception as e:
            print(f"❌ خطأ في تفعيل زر الشريط الأفقي: {e}")

    def create_main_ui(self):
        """إنشاء الواجهة الرئيسية"""
        # إزالة الأشرطة القديمة أولاً
        self.remove_old_toolbars()

        # إنشاء الحاوية الرئيسية مع شريط العنوان في الأعلى
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء شريط العنوان المخصص وإضافته في أعلى الحاوية
        self.create_custom_title_bar()
        main_layout.addWidget(self.custom_title_bar)

        # التأكد من ظهور شريط العنوان في المقدمة
        self.custom_title_bar.raise_()
        self.custom_title_bar.setVisible(True)
        self.custom_title_bar.show()

        # لا نفرض التحديث هنا لتجنب الوميض
        # self.custom_title_bar.update()
        # self.custom_title_bar.repaint()

        # طباعة معلومات التشخيص
        print(f"🔍 شريط العنوان عند الإنشاء: مرئي={self.custom_title_bar.isVisible()}, أبعاد={self.custom_title_bar.size()}")

        # إضافة فاصل رفيع وأنيق بين شريط العنوان والشريط الأفقي
        self.create_title_toolbar_separator()
        main_layout.addWidget(self.title_toolbar_separator)

        # إنشاء شريط الأدوات الأفقي وإضافته
        self.create_horizontal_toolbar_widget()
        main_layout.addWidget(self.h_toolbar_widget)

        # إنشاء التبويبات الرئيسية وإضافتها
        self.create_tabs()
        main_layout.addWidget(self.tabs)

        # إنشاء شريط الحالة المحفوظ قبل تعيين الحاوية المركزية
        self.create_saved_status_bar()

        # تعيين الحاوية الرئيسية كعنصر مركزي
        self.setCentralWidget(main_widget)

        # تحميل لوحة المعلومات فوراً
        self.load_dashboard_immediately()

        # تحديد التبويب الافتراضي فوراً (بدون تأخير لتجنب الوميض)
        self.set_default_tab()

        # إعادة تفعيل التحديث بعد إنشاء الواجهة
        QTimer.singleShot(1000, lambda: self.setUpdatesEnabled(True))



    def set_default_tab(self):
        """تحديد التبويب الافتراضي (لوحة المعلومات)"""
        try:
            print("🎯 تحديد التبويب الافتراضي: لوحة المعلومات")

            # التأكد من أن لوحة المعلومات محملة
            if self.dashboard_widget is None:
                self.load_dashboard_immediately()

            # التبديل إلى لوحة المعلومات (التبويب 0)
            self.tabs.setCurrentIndex(0)

            # تحديث حالة أزرار شريط الأدوات الجديد
            if hasattr(self, 'h_toolbar_buttons') and len(self.h_toolbar_buttons) > 0:
                # إلغاء تحديد جميع الأزرار
                for button in self.h_toolbar_buttons:
                    button.setChecked(False)
                # تحديد زر لوحة المعلومات
                self.h_toolbar_buttons[0].setChecked(True)

            print("✅ تم تحديد لوحة المعلومات كتبويب افتراضي")

        except Exception as e:
            print(f"❌ خطأ في تحديد التبويب الافتراضي: {e}")

    def create_saved_status_bar(self):
        """إنشاء شريط الحالة المحفوظ"""
        try:
            from ui.saved_status_bar import SavedStatusBar
            self.saved_status_bar = SavedStatusBar(self)
            self.saved_status_bar.create_status_bar()

            # لا نحتاج مؤقت إضافي لأن شريط الحالة المحفوظ له مؤقته الخاص

            print("✅ تم تحميل شريط الحالة المحفوظ بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل شريط الحالة المحفوظ: {e}")
            # في حالة الخطأ، استخدم شريط حالة بسيط
            self.create_simple_status_bar()

    def create_simple_status_bar(self):
        """إنشاء شريط حالة بسيط كبديل"""
        try:
            self.statusBar = QStatusBar()
            self.setStatusBar(self.statusBar)
            self.statusBar.showMessage("Smart Finish - جاهز للعمل")
            print("✅ تم إنشاء شريط الحالة البسيط")
        except Exception as e:
            print(f"❌ خطأ في إنشاء شريط الحالة البسيط: {e}")

    # تم حذف الدالة القديمة create_status_bar واستبدالها بـ create_saved_status_bar

    # تم حذف دالة create_advanced_left_features - غير مستخدمة

    # تم حذف الدوال القديمة غير المستخدمة:
    # - create_quick_control_buttons
    # - update_advanced_features
    # - instant_refresh
    # - toggle_notifications_advanced
    # - toggle_advanced_mode

    # ═══════════════════════════════════════════════════════════════
    # 🎯 دوال إنشاء أقسام شريط الحالة المتخصصة
    # ═══════════════════════════════════════════════════════════════

    def create_financial_business_section(self):
        """📊 إنشاء قسم الإحصائيات المالية والأعمال"""
        try:
            from PyQt5.QtWidgets import QHBoxLayout

            # إنشاء حاوية القسم المالي
            financial_widget = QWidget()
            financial_layout = QHBoxLayout(financial_widget)
            financial_layout.setContentsMargins(4, 2, 4, 2)
            financial_layout.setSpacing(8)

            # 💰 الإحصائيات المالية الرئيسية
            self.financial_stats_label = QLabel("💰 الرصيد: جاري التحميل...")
            self.financial_stats_label.setMinimumWidth(220)
            self.financial_stats_label.setProperty("financial", True)
            self.financial_stats_label.setToolTip("💰 الإحصائيات المالية الفورية\n• الأرباح والخسائر\n• أرصدة العملاء والموردين\n• إجمالي الإيرادات والمصروفات")
            financial_layout.addWidget(self.financial_stats_label)

            # 📊 مؤشرات الأعمال
            self.business_stats_label = QLabel("📊 العملاء: جاري التحميل...")
            self.business_stats_label.setMinimumWidth(180)
            self.business_stats_label.setProperty("performance", True)
            self.business_stats_label.setToolTip("📊 مؤشرات الأعمال والإحصائيات\n• عدد العملاء والموردين\n• المشاريع النشطة\n• منتجات المخزون")
            financial_layout.addWidget(self.business_stats_label)

            # إضافة فاصل أنيق
            separator1 = self.create_elegant_separator()
            financial_layout.addWidget(separator1)

            # إضافة القسم لشريط الحالة
            self.statusBar.addWidget(financial_widget)

        except Exception as e:
            print(f"خطأ في إنشاء القسم المالي: {str(e)}")

    def create_performance_monitoring_section(self):
        """⚡ إنشاء قسم مراقبة الأداء والعمليات"""
        try:
            from PyQt5.QtWidgets import QHBoxLayout, QProgressBar

            # إنشاء حاوية قسم الأداء
            performance_widget = QWidget()
            performance_layout = QHBoxLayout(performance_widget)
            performance_layout.setContentsMargins(4, 2, 4, 2)
            performance_layout.setSpacing(6)

            # 🚀 مؤشر أداء النظام
            self.performance_indicator = QLabel("🚀 ممتاز")
            self.performance_indicator.setMinimumWidth(90)
            self.performance_indicator.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #43e97b, stop:0.5 #38f9d7, stop:1 #4facfe);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 6px;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    font-size: 11px;
                    font-weight: bold;

                }
            """)
            self.performance_indicator.setToolTip("🚀 مؤشر أداء النظام\n• استخدام المعالج والذاكرة\n• سرعة قاعدة البيانات\n• حالة النظام العامة")
            performance_layout.addWidget(self.performance_indicator)

            # ⚡ مؤشر حالة العمليات
            self.operation_status_label = QLabel("⚡ جاهز")
            self.operation_status_label.setMinimumWidth(80)
            self.operation_status_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: white;
                    padding: 4px 8px;
                    border-radius: 6px;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    font-size: 11px;
                    font-weight: bold;

                }
            """)
            self.operation_status_label.setToolTip("⚡ حالة العمليات الجارية")
            performance_layout.addWidget(self.operation_status_label)

            # 📋 مؤشر الأنشطة الحديثة
            self.recent_activity_label = QLabel("📋 لا توجد أنشطة")
            self.recent_activity_label.setMinimumWidth(140)
            self.recent_activity_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #fee140, stop:0.5 #fa709a, stop:1 #f093fb);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 6px;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    font-size: 11px;
                    font-weight: bold;

                }
            """)
            self.recent_activity_label.setToolTip("📋 آخر الأنشطة المنجزة")
            performance_layout.addWidget(self.recent_activity_label)

            # مؤشر التقدم (مخفي افتراضياً)
            self.general_progress = QProgressBar()
            self.general_progress.setMinimumWidth(80)
            self.general_progress.setMaximumWidth(100)
            self.general_progress.setMinimumHeight(20)
            self.general_progress.setMaximumHeight(20)
            self.general_progress.setRange(0, 100)
            self.general_progress.setValue(0)
            self.general_progress.setVisible(False)
            self.general_progress.setStyleSheet("""
                QProgressBar {
                    background: rgba(255, 255, 255, 0.3);
                    border: 1px solid rgba(255, 255, 255, 0.5);
                    border-radius: 10px;
                    text-align: center;
                    font-size: 10px;
                    color: white;
                    font-weight: bold;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #43e97b, stop:0.5 #38f9d7, stop:1 #4facfe);
                    border-radius: 9px;
                }
            """)
            performance_layout.addWidget(self.general_progress)

            # إضافة فاصل أنيق
            separator2 = self.create_elegant_separator()
            performance_layout.addWidget(separator2)

            # إضافة القسم لشريط الحالة
            self.statusBar.addPermanentWidget(performance_widget)

            # تهيئة قائمة الأنشطة ومراقبة الأداء
            self.recent_activities = []
            self.start_performance_monitoring()

        except Exception as e:
            print(f"خطأ في إنشاء قسم مراقبة الأداء: {str(e)}")

    def create_smart_notifications_section(self):
        """🔔 إنشاء قسم التنبيهات والإشعارات الذكية"""
        try:
            from PyQt5.QtWidgets import QHBoxLayout

            # إنشاء حاوية قسم التنبيهات
            notifications_widget = QWidget()
            notifications_layout = QHBoxLayout(notifications_widget)
            notifications_layout.setContentsMargins(4, 2, 4, 2)
            notifications_layout.setSpacing(6)

            # 🔔 التنبيهات الذكية
            self.smart_alerts_label = QLabel("🔔 لا توجد تنبيهات")
            self.smart_alerts_label.setMinimumWidth(160)
            self.smart_alerts_label.setProperty("alerts", True)
            self.smart_alerts_label.setToolTip("🔔 التنبيهات والإشعارات المهمة\n• فواتير متأخرة\n• مصروفات مستحقة\n• إشعارات جديدة")
            notifications_layout.addWidget(self.smart_alerts_label)

            # 💾 معلومات النظام
            self.system_info_label = QLabel("💾 النظام: جاهز")
            self.system_info_label.setMinimumWidth(140)
            self.system_info_label.setProperty("system", True)
            self.system_info_label.setToolTip("💾 معلومات النظام وقاعدة البيانات\n• حالة الاتصال\n• استخدام الذاكرة\n• حالة النظام")
            notifications_layout.addWidget(self.system_info_label)

            # إضافة فاصل أنيق
            separator3 = self.create_elegant_separator()
            notifications_layout.addWidget(separator3)

            # إضافة القسم لشريط الحالة
            self.statusBar.addPermanentWidget(notifications_widget)

        except Exception as e:
            print(f"خطأ في إنشاء قسم التنبيهات: {str(e)}")

    def create_elegant_separator(self):
        """إنشاء فاصل أنيق بين الأقسام"""
        separator = QLabel("│")
        separator.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.4);
                font-size: 16px;
                font-weight: bold;
                padding: 0px 4px;

            }
        """)
        separator.setMinimumWidth(10)
        separator.setMaximumWidth(10)
        return separator

    def create_control_management_section(self):
        """🎛️ إنشاء قسم أدوات التحكم والإدارة"""
        try:
            from PyQt5.QtWidgets import QHBoxLayout, QPushButton

            # إنشاء حاوية قسم التحكم
            control_widget = QWidget()
            control_layout = QHBoxLayout(control_widget)
            control_layout.setContentsMargins(4, 2, 4, 2)
            control_layout.setSpacing(4)

            # نمط أزرار التحكم المتطور
            control_button_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.3),
                        stop:1 rgba(255, 255, 255, 0.2));
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    border-radius: 4px;
                    padding: 2px 6px;
                    font-size: 10px;
                    font-weight: bold;
                    min-width: 40px;
                    max-width: 50px;

                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.5),
                        stop:1 rgba(255, 255, 255, 0.3));
                    border: 1px solid rgba(255, 255, 255, 0.6);
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #43e97b, stop:1 #38f9d7);
                    border: 1px solid rgba(255, 255, 255, 0.8);
                }
            """

            # 🔄 زر التحديث التلقائي
            self.auto_update_btn = QPushButton("🔄")
            self.auto_update_btn.setCheckable(True)
            self.auto_update_btn.setChecked(True)
            self.auto_update_btn.setStyleSheet(control_button_style)
            self.auto_update_btn.setToolTip("🔄 تشغيل/إيقاف التحديث التلقائي")
            self.auto_update_btn.clicked.connect(self.toggle_auto_update)
            control_layout.addWidget(self.auto_update_btn)

            # 🔔 زر الإشعارات
            self.notifications_btn = QPushButton("🔔")
            self.notifications_btn.setCheckable(True)
            self.notifications_btn.setChecked(True)
            self.notifications_btn.setStyleSheet(control_button_style)
            self.notifications_btn.setToolTip("🔔 تشغيل/إيقاف الإشعارات")
            self.notifications_btn.clicked.connect(self.toggle_notifications)
            control_layout.addWidget(self.notifications_btn)

            # 🌙 زر الوضع المظلم/الفاتح
            self.theme_btn = QPushButton("🌙")
            self.theme_btn.setCheckable(True)
            self.theme_btn.setStyleSheet(control_button_style)
            self.theme_btn.setToolTip("🌙 تبديل الوضع المظلم/الفاتح")
            self.theme_btn.clicked.connect(self.toggle_theme)
            control_layout.addWidget(self.theme_btn)

            # إضافة أزرار الوصول السريع
            self.add_quick_action_buttons_to_layout(control_layout, control_button_style)

            # إضافة فاصل أنيق
            separator4 = self.create_elegant_separator()
            control_layout.addWidget(separator4)

            # إضافة القسم لشريط الحالة
            self.statusBar.addPermanentWidget(control_widget)

        except Exception as e:
            print(f"خطأ في إنشاء قسم التحكم: {str(e)}")

    def create_datetime_info_section(self):
        """🕐 إنشاء قسم الوقت والمعلومات الإضافية"""
        try:
            from PyQt5.QtWidgets import QHBoxLayout

            # إنشاء حاوية قسم الوقت
            datetime_widget = QWidget()
            datetime_layout = QHBoxLayout(datetime_widget)
            datetime_layout.setContentsMargins(4, 2, 4, 2)
            datetime_layout.setSpacing(6)

            # 🕐 الوقت والتاريخ المتطور
            self.datetime_label = QLabel()
            self.datetime_label.setMinimumWidth(250)
            self.datetime_label.setProperty("datetime", True)
            self.datetime_label.setToolTip("🕐 التاريخ والوقت الحالي مع معلومات إضافية\n• التحيات حسب الوقت\n• اسم اليوم بالعربية\n• المنطقة الزمنية")
            datetime_layout.addWidget(self.datetime_label)

            # إضافة القسم لشريط الحالة
            self.statusBar.addPermanentWidget(datetime_widget)

        except Exception as e:
            print(f"خطأ في إنشاء قسم الوقت: {str(e)}")

    def add_quick_action_buttons_to_layout(self, layout, button_style):
        """إضافة أزرار الوصول السريع للتخطيط المحدد"""
        try:
            from PyQt5.QtWidgets import QPushButton

            # 🔄 زر تحديث سريع
            refresh_btn = QPushButton("🔄")
            refresh_btn.setStyleSheet(button_style)
            refresh_btn.setToolTip("🔄 تحديث جميع البيانات فوراً")
            refresh_btn.clicked.connect(self.quick_refresh_all)
            layout.addWidget(refresh_btn)

            # 💾 زر النسخ الاحتياطي
            backup_btn = QPushButton("💾")
            backup_btn.setStyleSheet(button_style)
            backup_btn.setToolTip("💾 إنشاء نسخة احتياطية سريعة")
            backup_btn.clicked.connect(self.quick_backup)
            layout.addWidget(backup_btn)

            # 📊 زر التقارير
            reports_btn = QPushButton("📊")
            reports_btn.setStyleSheet(button_style)
            reports_btn.setToolTip("📊 الوصول السريع للتقارير")
            reports_btn.clicked.connect(self.quick_reports)
            layout.addWidget(reports_btn)

            # ⚙️ زر الإعدادات
            settings_btn = QPushButton("⚙️")
            settings_btn.setStyleSheet(button_style)
            settings_btn.setToolTip("⚙️ الوصول السريع للإعدادات")
            settings_btn.clicked.connect(self.quick_settings)
            layout.addWidget(settings_btn)

        except Exception as e:
            print(f"خطأ في إضافة أزرار الوصول السريع: {str(e)}")

    def add_quick_action_buttons(self):
        """دالة قديمة - تم استبدالها بالأقسام المنظمة"""
        # هذه الدالة لم تعد مستخدمة - تم دمج الأزرار في الأقسام المنظمة
        pass

    def quick_refresh_all(self):
        """تحديث سريع لجميع البيانات"""
        try:
            # تحديث شريط الحالة فوراً
            self.update_advanced_status()

            # تحديث التبويب الحالي إذا كان متاحاً
            current_tab = self.tabs.currentWidget()
            if current_tab and hasattr(current_tab, 'refresh_data'):
                current_tab.refresh_data()

            # عرض رسالة تأكيد مؤقتة (تأخير أكبر لتجنب الوميض)
            if hasattr(self, 'system_info_label'):
                original_text = self.system_info_label.text()
                self.system_info_label.setText("✅ تم التحديث")
                QTimer.singleShot(10000, lambda: self.system_info_label.setText(original_text))

        except Exception as e:
            print(f"خطأ في التحديث السريع: {str(e)}")

    def quick_backup(self):
        """نسخة احتياطية سريعة"""
        try:
            # استدعاء دالة النسخ الاحتياطي إذا كانت متاحة
            if hasattr(self, 'backup_database'):
                self.backup_database()
            else:
                QMessageBox.information(self, "النسخ الاحتياطي", "🔧 سيتم تطوير النسخ الاحتياطي السريع قريباً!")

        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي السريع: {str(e)}")

    def quick_reports(self):
        """الوصول السريع للتقارير"""
        try:
            # الانتقال لتبويب التقارير
            self.simple_switch_tab(10)  # فهرس تبويب التقارير

        except Exception as e:
            print(f"خطأ في الوصول السريع للتقارير: {str(e)}")

    def quick_settings(self):
        """الوصول السريع للإعدادات"""
        try:
            # عرض رسالة الإعدادات
            self.show_settings_message()

        except Exception as e:
            print(f"خطأ في الوصول السريع للإعدادات: {str(e)}")

    def add_progress_indicators(self):
        """إضافة مؤشرات التقدم والأنشطة"""
        try:
            from PyQt5.QtWidgets import QProgressBar, QHBoxLayout

            # إنشاء حاوية لمؤشرات التقدم
            progress_widget = QWidget()
            progress_layout = QHBoxLayout(progress_widget)
            progress_layout.setContentsMargins(2, 2, 2, 2)
            progress_layout.setSpacing(4)

            # مؤشر التقدم العام
            self.general_progress = QProgressBar()
            self.general_progress.setMinimumWidth(80)
            self.general_progress.setMaximumWidth(100)
            self.general_progress.setMinimumHeight(20)
            self.general_progress.setMaximumHeight(20)
            self.general_progress.setRange(0, 100)
            self.general_progress.setValue(0)
            self.general_progress.setVisible(False)  # مخفي افتراضياً
            self.general_progress.setStyleSheet("""
                QProgressBar {
                    background: rgba(255, 255, 255, 0.3);
                    border: 1px solid rgba(255, 255, 255, 0.5);
                    border-radius: 10px;
                    text-align: center;
                    font-size: 10px;
                    color: white;
                    font-weight: bold;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #43e97b, stop:0.5 #38f9d7, stop:1 #4facfe);
                    border-radius: 9px;
                }
            """)
            progress_layout.addWidget(self.general_progress)

            # مؤشر حالة العمليات
            self.operation_status_label = QLabel("⚡ جاهز")
            self.operation_status_label.setMinimumWidth(80)
            self.operation_status_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: white;
                    padding: 4px 8px;
                    border-radius: 6px;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    font-size: 11px;
                    font-weight: bold;

                }
            """)
            self.operation_status_label.setToolTip("حالة العمليات الجارية")
            progress_layout.addWidget(self.operation_status_label)

            # إضافة الحاوية لشريط الحالة
            self.statusBar.addPermanentWidget(progress_widget)

        except Exception as e:
            print(f"خطأ في إضافة مؤشرات التقدم: {str(e)}")

    def add_network_performance_stats(self):
        """إضافة إحصائيات الشبكة والأداء"""
        try:
            # مؤشر أداء النظام
            self.performance_indicator = QLabel("🚀 ممتاز")
            self.performance_indicator.setMinimumWidth(90)
            self.performance_indicator.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #43e97b, stop:0.5 #38f9d7, stop:1 #4facfe);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 6px;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    font-size: 11px;
                    font-weight: bold;

                }
            """)
            self.performance_indicator.setToolTip("مؤشر أداء النظام")
            self.statusBar.addPermanentWidget(self.performance_indicator)

            # بدء مراقبة الأداء
            self.start_performance_monitoring()

        except Exception as e:
            print(f"خطأ في إضافة إحصائيات الأداء: {str(e)}")

    def add_recent_activities_indicator(self):
        """إضافة مؤشر الأنشطة الحديثة"""
        try:
            # مؤشر آخر نشاط
            self.recent_activity_label = QLabel("📋 لا توجد أنشطة")
            self.recent_activity_label.setMinimumWidth(120)
            self.recent_activity_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #fee140, stop:0.5 #fa709a, stop:1 #f093fb);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 6px;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    font-size: 11px;
                    font-weight: bold;

                }
            """)
            self.recent_activity_label.setToolTip("آخر الأنشطة المنجزة")
            self.statusBar.addPermanentWidget(self.recent_activity_label)

            # قائمة لتتبع الأنشطة
            self.recent_activities = []

        except Exception as e:
            print(f"خطأ في إضافة مؤشر الأنشطة: {str(e)}")

    def add_advanced_control_buttons(self):
        """إضافة أزرار التحكم السريع المتطورة"""
        try:
            from PyQt5.QtWidgets import QPushButton, QHBoxLayout

            # إنشاء حاوية لأزرار التحكم
            control_widget = QWidget()
            control_layout = QHBoxLayout(control_widget)
            control_layout.setContentsMargins(2, 2, 2, 2)
            control_layout.setSpacing(2)

            # نمط أزرار التحكم
            control_button_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.3),
                        stop:1 rgba(255, 255, 255, 0.2));
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    border-radius: 4px;
                    padding: 2px 6px;
                    font-size: 10px;
                    font-weight: bold;
                    min-width: 40px;
                    max-width: 50px;

                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.5),
                        stop:1 rgba(255, 255, 255, 0.3));
                    border: 1px solid rgba(255, 255, 255, 0.6);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.2),
                        stop:1 rgba(255, 255, 255, 0.1));
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #43e97b, stop:1 #38f9d7);
                    border: 1px solid rgba(255, 255, 255, 0.8);
                }
            """

            # زر تشغيل/إيقاف التحديث التلقائي
            self.auto_update_btn = QPushButton("🔄")
            self.auto_update_btn.setCheckable(True)
            self.auto_update_btn.setChecked(True)
            self.auto_update_btn.setStyleSheet(control_button_style)
            self.auto_update_btn.setToolTip("تشغيل/إيقاف التحديث التلقائي")
            self.auto_update_btn.clicked.connect(self.toggle_auto_update)
            control_layout.addWidget(self.auto_update_btn)

            # زر تشغيل/إيقاف الإشعارات
            self.notifications_btn = QPushButton("🔔")
            self.notifications_btn.setCheckable(True)
            self.notifications_btn.setChecked(True)
            self.notifications_btn.setStyleSheet(control_button_style)
            self.notifications_btn.setToolTip("تشغيل/إيقاف الإشعارات")
            self.notifications_btn.clicked.connect(self.toggle_notifications)
            control_layout.addWidget(self.notifications_btn)

            # زر الوضع المظلم/الفاتح
            self.theme_btn = QPushButton("🌙")
            self.theme_btn.setCheckable(True)
            self.theme_btn.setStyleSheet(control_button_style)
            self.theme_btn.setToolTip("تبديل الوضع المظلم/الفاتح")
            self.theme_btn.clicked.connect(self.toggle_theme)
            control_layout.addWidget(self.theme_btn)

            # إضافة الحاوية لشريط الحالة
            self.statusBar.addPermanentWidget(control_widget)

        except Exception as e:
            print(f"خطأ في إضافة أزرار التحكم: {str(e)}")



    def add_status_bar_animations(self):
        """إضافة تأثيرات بصرية متطورة لشريط الحالة"""
        try:
            # تأثير نبض للتنبيهات المهمة
            self.create_alert_pulse_animation()

            # تأثير تدرج للإحصائيات المالية
            self.create_financial_glow_animation()

            # تأثير تحديث للوقت
            self.create_datetime_update_animation()

        except Exception as e:
            print(f"خطأ في إضافة تأثيرات شريط الحالة: {str(e)}")

    def create_alert_pulse_animation(self):
        """إنشاء تأثير نبض للتنبيهات"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve
            from PyQt5.QtWidgets import QGraphicsOpacityEffect

            # إضافة تأثير الشفافية للتنبيهات
            self.alert_opacity_effect = QGraphicsOpacityEffect()
            self.smart_alerts_label.setGraphicsEffect(self.alert_opacity_effect)

            # إنشاء الرسوم المتحركة للنبض
            self.alert_pulse_animation = QPropertyAnimation(self.alert_opacity_effect, b"opacity")
            self.alert_pulse_animation.setDuration(1500)  # مدة النبضة
            self.alert_pulse_animation.setStartValue(0.7)
            self.alert_pulse_animation.setEndValue(1.0)
            self.alert_pulse_animation.setEasingCurve(QEasingCurve.InOutSine)
            self.alert_pulse_animation.setLoopCount(-1)  # تكرار لا نهائي

            # بدء التأثير
            self.alert_pulse_animation.start()

        except Exception as e:
            print(f"خطأ في إنشاء تأثير نبض التنبيهات: {str(e)}")

    def create_financial_glow_animation(self):
        """إنشاء تأثير توهج للإحصائيات المالية"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve
            from PyQt5.QtWidgets import QGraphicsOpacityEffect

            # إضافة تأثير الشفافية للإحصائيات المالية
            self.financial_opacity_effect = QGraphicsOpacityEffect()
            self.financial_stats_label.setGraphicsEffect(self.financial_opacity_effect)

            # إنشاء الرسوم المتحركة للتوهج
            self.financial_glow_animation = QPropertyAnimation(self.financial_opacity_effect, b"opacity")
            self.financial_glow_animation.setDuration(3000)  # مدة التوهج
            self.financial_glow_animation.setStartValue(0.8)
            self.financial_glow_animation.setEndValue(1.0)
            self.financial_glow_animation.setEasingCurve(QEasingCurve.InOutQuad)
            self.financial_glow_animation.setLoopCount(-1)  # تكرار لا نهائي

            # بدء التأثير
            self.financial_glow_animation.start()

        except Exception as e:
            print(f"خطأ في إنشاء تأثير توهج الإحصائيات المالية: {str(e)}")

    def create_datetime_update_animation(self):
        """إنشاء تأثير تحديث للوقت والتاريخ"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve
            from PyQt5.QtWidgets import QGraphicsOpacityEffect

            # إضافة تأثير الشفافية للوقت والتاريخ
            self.datetime_opacity_effect = QGraphicsOpacityEffect()
            self.datetime_label.setGraphicsEffect(self.datetime_opacity_effect)

            # إنشاء الرسوم المتحركة للتحديث
            self.datetime_update_animation = QPropertyAnimation(self.datetime_opacity_effect, b"opacity")
            self.datetime_update_animation.setDuration(500)  # مدة التحديث
            self.datetime_update_animation.setStartValue(0.5)
            self.datetime_update_animation.setEndValue(1.0)
            self.datetime_update_animation.setEasingCurve(QEasingCurve.OutBounce)

            # ربط التأثير بتحديث الوقت
            self.datetime_timer.timeout.connect(self.trigger_datetime_animation)

        except Exception as e:
            print(f"خطأ في إنشاء تأثير تحديث الوقت: {str(e)}")

    def trigger_datetime_animation(self):
        """تشغيل تأثير تحديث الوقت"""
        try:
            if hasattr(self, 'datetime_update_animation'):
                self.datetime_update_animation.start()
        except Exception as e:
            print(f"خطأ في تشغيل تأثير تحديث الوقت: {str(e)}")

    def update_status_bar_theme(self, is_urgent=False):
        """تحديث مظهر شريط الحالة حسب الحالة"""
        try:
            if is_urgent:
                # تغيير لون شريط الحالة للحالات العاجلة
                urgent_style = """
                    QStatusBar {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #ff6b6b, stop:0.3 #ee5a52, stop:0.6 #ff4757, stop:1 #ff3838);
                        border-top: 4px solid #ffffff;
                    }
                """
                self.statusBar.setStyleSheet(urgent_style)

                # تشغيل تأثير النبض العاجل
                if hasattr(self, 'alert_pulse_animation'):
                    self.alert_pulse_animation.setDuration(800)  # نبض أسرع
            else:
                # العودة للمظهر العادي
                self.create_status_bar()  # إعادة تطبيق النمط العادي

        except Exception as e:
            print(f"خطأ في تحديث مظهر شريط الحالة: {str(e)}")



    def create_horizontal_toolbar_widget(self):
        """إنشاء شريط الأدوات الأفقي كـ Widget"""
        try:
            print("🚀 إنشاء شريط الأدوات الأفقي كـ Widget...")

            # إنشاء الحاوية الرئيسية للشريط
            self.h_toolbar_widget = QWidget()
            self.h_toolbar_widget.setObjectName("horizontalToolbarWidget")
            self.h_toolbar_widget.setFixedHeight(60)

            # إنشاء التخطيط الأفقي
            toolbar_layout = QHBoxLayout(self.h_toolbar_widget)
            toolbar_layout.setContentsMargins(5, 5, 5, 5)
            toolbar_layout.setSpacing(5)

            # تطبيق النمط المتطور
            self.h_toolbar_widget.setStyleSheet("""
                QWidget#horizontalToolbarWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3B82F6, stop:0.1 #2563EB, stop:0.2 #1E40AF,
                        stop:0.3 #1E293B, stop:0.4 #0F172A, stop:0.5 #000000,
                        stop:0.6 #0F172A, stop:0.7 #1E293B, stop:0.8 #1E40AF,
                        stop:0.9 #2563EB, stop:1 #3B82F6);
                    border: none;
                    border-top: 2px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, 0.3), stop:0.5 rgba(255, 255, 255, 0.5), stop:1 rgba(255, 255, 255, 0.3));
                    border-bottom: 5px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #ff6b6b, stop:0.15 #ffd700, stop:0.3 #00ff7f,
                        stop:0.45 #00bfff, stop:0.6 #ff69b4, stop:0.75 #9370db,
                        stop:0.9 #ffd700, stop:1 #ff6b6b);
                    padding: 5px;
                    min-height: 60px;
                    max-height: 60px;
                }
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(100, 100, 100, 0.4),
                        stop:0.3 rgba(80, 80, 80, 0.3),
                        stop:0.7 rgba(60, 60, 60, 0.25),
                        stop:1 rgba(40, 40, 40, 0.2));
                    border: 2px solid rgba(120, 120, 120, 0.6);
                    border-radius: 12px;
                    padding: 16px 12px;
                    margin: 2px;
                    color: #ffffff;
                    font-weight: 700;
                    font-size: 13pt;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    min-width: 120px;
                    max-width: 150px;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.6),
                        stop:0.2 rgba(0, 255, 255, 0.4),
                        stop:0.4 rgba(255, 255, 255, 0.5),
                        stop:0.6 rgba(255, 215, 0, 0.3),
                        stop:0.8 rgba(255, 255, 255, 0.4),
                        stop:1 rgba(255, 255, 255, 0.2));
                    border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.5 rgba(0, 255, 255, 0.8),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border-radius: 12px;
                    padding: 16px 12px;
                    margin: 2px;
                    box-sizing: border-box;
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 215, 0, 0.6),
                        stop:0.3 rgba(255, 255, 255, 0.5),
                        stop:0.7 rgba(255, 215, 0, 0.4),
                        stop:1 rgba(255, 255, 255, 0.3));
                    border: 3px solid rgba(255, 215, 0, 0.9);
                    border-radius: 12px;
                    padding: 16px 12px;
                    margin: 2px;
                    font-weight: 800;
                    font-size: 13pt;
                    color: #ffffff;
                    box-sizing: border-box;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 215, 0, 0.8),
                        stop:0.3 rgba(255, 255, 255, 0.6),
                        stop:0.7 rgba(255, 215, 0, 0.5),
                        stop:1 rgba(255, 255, 255, 0.4));
                    border: 3px solid rgba(255, 215, 0, 1.0);
                    border-radius: 12px;
                    padding: 16px 12px;
                    margin: 2px;
                    font-weight: 900;
                    box-sizing: border-box;
                }
            """)

            # قائمة لحفظ الأزرار
            self.h_toolbar_buttons = []

            # إنشاء الأزرار بأيقونات متطورة ومتقدمة
            buttons_data = [
                ("🌟 لوحة المعلومات", 0, "F1"),
                ("🤝 إدارة العملاء", 1, "F2"),
                ("🚛 إدارة الموردين", 2, "F3"),
                ("👷‍♂️ إدارة العمال", 3, "F4"),
                ("🏗️ إدارة المشاريع", 4, "F5"),
                ("🏪 إدارة المخازن", 5, "F6"),
                ("💰 إدارة المصروفات", 6, "F7"),
                ("💵 إدارة الإيرادات", 7, "F8"),
                ("📋 إدارة الفواتير", 8, "F9"),
                ("🔔 الإشعارات", 9, "F10"),
                ("📊 التقارير", 10, "F11"),
                ("⚙️ الإعدادات", None, "F12")
            ]

            for text, tab_index, shortcut in buttons_data:
                button = QPushButton(text)
                button.setCheckable(True)

                if tab_index is not None:
                    button.clicked.connect(lambda _, idx=tab_index, btn=button: self.switch_tab(idx, None, btn))
                else:
                    button.clicked.connect(self.show_settings_message)

                button.setToolTip(f"{text}\n⌨️ اختصار لوحة المفاتيح: {shortcut}")

                toolbar_layout.addWidget(button)
                self.h_toolbar_buttons.append(button)

            # تحديد الزر الأول كنشط
            if self.h_toolbar_buttons:
                self.h_toolbar_buttons[0].setChecked(True)

            print("✅ تم إنشاء شريط الأدوات الأفقي كـ Widget بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إنشاء شريط الأدوات الأفقي كـ Widget: {e}")

    def fix_toolbar_button_positions(self):
        """تثبيت مواضع أزرار شريط الأدوات لمنع تغيير المكان"""
        try:
            if hasattr(self, 'h_toolbar_buttons') and hasattr(self, 'h_toolbar_widget'):
                # إعادة تطبيق التخطيط لضمان ثبات المواضع
                layout = self.h_toolbar_widget.layout()
                if layout:
                    # تثبيت حجم وموضع كل زر
                    for i, button in enumerate(self.h_toolbar_buttons):
                        button.setMinimumWidth(120)  # العرض الأصلي
                        button.setMaximumWidth(150)  # العرض الأصلي
                        button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

                    # فرض تحديث التخطيط
                    layout.update()
                    self.h_toolbar_widget.updateGeometry()

        except Exception as e:
            print(f"خطأ في تثبيت مواضع الأزرار: {str(e)}")



    def remove_all_toolbars(self):
        """إزالة جميع الأشرطة تماماً"""
        try:
            print("🗑️ بدء إزالة جميع الأشرطة...")

            # تم إزالة جميع مراجع الشريط الجانبي

            # إزالة الشريط الأفقي إذا كان موجوداً
            if hasattr(self, 'h_toolbar'):
                self.removeToolBar(self.h_toolbar)
                delattr(self, 'h_toolbar')
                print("✅ تم إزالة الشريط الأفقي")

            # إزالة جميع أشرطة الأدوات الموجودة
            all_toolbars = self.findChildren(QToolBar)
            for toolbar in all_toolbars:
                self.removeToolBar(toolbar)
                print(f"✅ تم إزالة شريط: {toolbar.objectName()}")

            # إزالة أي متغيرات مرتبطة بالأشرطة
            toolbar_attrs = ['h_toolbar_actions', 'enhanced_toolbar_opacity_effect',
                            'enhanced_toolbar_fade_animation', 'ultra_toolbar_actions', 'ultra_toolbar']
            for attr in toolbar_attrs:
                if hasattr(self, attr):
                    delattr(self, attr)
                    print(f"✅ تم إزالة متغير: {attr}")

            print("🎉 تم إزالة جميع الأشرطة بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إزالة الأشرطة: {str(e)}")

    def remove_old_toolbars(self):
        """إزالة أشرطة الأدوات القديمة فقط (ليس شريط العنوان المخصص)"""
        try:
            print("🗑️ بدء إزالة الأشرطة القديمة...")

            # إزالة الشريط الأفقي القديم إذا كان موجوداً
            if hasattr(self, 'h_toolbar'):
                self.removeToolBar(self.h_toolbar)
                delattr(self, 'h_toolbar')
                print("✅ تم إزالة الشريط الأفقي القديم")

            # إزالة أشرطة الأدوات القديمة فقط (تجنب شريط العنوان المخصص)
            all_toolbars = self.findChildren(QToolBar)
            for toolbar in all_toolbars:
                # تجنب حذف شريط العنوان المخصص
                if toolbar.objectName() not in ["titleToolbar", "customTitleToolbar"]:
                    self.removeToolBar(toolbar)
                    print(f"✅ تم إزالة شريط قديم: {toolbar.objectName()}")
                else:
                    print(f"🔒 تم الاحتفاظ بشريط العنوان المخصص: {toolbar.objectName()}")

            # إزالة أي متغيرات مرتبطة بالأشرطة القديمة
            toolbar_attrs = ['h_toolbar_actions', 'enhanced_toolbar_opacity_effect',
                            'enhanced_toolbar_fade_animation', 'ultra_toolbar_actions', 'ultra_toolbar']
            for attr in toolbar_attrs:
                if hasattr(self, attr):
                    delattr(self, attr)
                    print(f"✅ تم إزالة متغير قديم: {attr}")

            print("🎉 تم إزالة الأشرطة القديمة بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إزالة الأشرطة القديمة: {str(e)}")

    def show_settings_message(self):
        """عرض رسالة الإعدادات"""
        QMessageBox.information(self, "الإعدادات", "🔧 سيتم تطوير قسم الإعدادات قريباً!")

    # تم حذف دالة create_simple_effective_toolbar - لا تُستخدم بعد الآن

    # تم حذف دوال الشريط الجانبي القديم

    # تم حذف جميع دوال الشريط الجانبي المتطور

    # تم حذف جميع دوال الشريط الجانبي المتطور









    def create_pulse_animation(self, widget):
        """إنشاء تأثير النبض للعنصر"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve, QSequentialAnimationGroup
            from PyQt5.QtWidgets import QGraphicsOpacityEffect

            opacity_effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(opacity_effect)

            # تأثير النبض
            pulse_animation = QSequentialAnimationGroup()

            fade_out = QPropertyAnimation(opacity_effect, b"opacity")
            fade_out.setDuration(800)
            fade_out.setStartValue(1.0)
            fade_out.setEndValue(0.7)
            fade_out.setEasingCurve(QEasingCurve.InOutSine)

            fade_in = QPropertyAnimation(opacity_effect, b"opacity")
            fade_in.setDuration(800)
            fade_in.setStartValue(0.7)
            fade_in.setEndValue(1.0)
            fade_in.setEasingCurve(QEasingCurve.InOutSine)

            pulse_animation.addAnimation(fade_out)
            pulse_animation.addAnimation(fade_in)
            pulse_animation.setLoopCount(-1)  # تكرار لا نهائي

            return pulse_animation

        except Exception as e:
            print(f"خطأ في إنشاء تأثير النبض: {str(e)}")
            return None

    def update_advanced_status(self):
        """تحديث شريط الحالة المتطور مع جميع الإحصائيات"""
        try:
            # تحديث الإحصائيات المالية
            self.update_financial_stats()

            # تحديث مؤشرات الأداء
            self.update_performance_stats()

            # تحديث التنبيهات الذكية
            self.update_smart_alerts()

            # تحديث معلومات النظام
            self.update_system_info()

            # تحديث الوقت والتاريخ
            self.update_datetime_only()

        except Exception as e:
            print(f"خطأ في تحديث شريط الحالة المتطور: {str(e)}")

    def update_financial_stats(self):
        """تحديث الإحصائيات المالية"""
        try:
            from database import Revenue, Expense, Client, Supplier
            from sqlalchemy import func

            # حساب إجمالي الإيرادات
            total_revenues = self.session.query(func.sum(Revenue.amount)).scalar() or 0

            # حساب إجمالي المصروفات
            total_expenses = self.session.query(func.sum(Expense.amount)).scalar() or 0

            # حساب صافي الربح
            net_profit = total_revenues - total_expenses

            # حساب أرصدة العملاء
            clients_balance = self.session.query(func.sum(Client.balance)).scalar() or 0

            # حساب أرصدة الموردين
            suppliers_balance = self.session.query(func.sum(Supplier.balance)).scalar() or 0

            # تحديد النص حسب الحالة المالية
            if net_profit > 0:
                profit_text = f"ربح: {net_profit:,.0f}"
            elif net_profit < 0:
                profit_text = f"خسارة: {abs(net_profit):,.0f}"
            else:
                profit_text = "متوازن"

            # تحديث النص
            financial_text = f"💰 {profit_text} | عملاء: {clients_balance:,.0f}"
            self.financial_stats_label.setText(financial_text)
            self.financial_stats_label.setToolTip(
                f"الإحصائيات المالية:\n"
                f"• إجمالي الإيرادات: {total_revenues:,.0f} ج.م\n"
                f"• إجمالي المصروفات: {total_expenses:,.0f} ج.م\n"
                f"• صافي الربح: {net_profit:,.0f} ج.م\n"
                f"• أرصدة العملاء: {clients_balance:,.0f} ج.م\n"
                f"• أرصدة الموردين: {suppliers_balance:,.0f} ج.م"
            )

        except Exception as e:
            self.financial_stats_label.setText("💰 خطأ في البيانات المالية")
            print(f"خطأ في تحديث الإحصائيات المالية: {str(e)}")

    def update_performance_stats(self):
        """تحديث مؤشرات الأداء"""
        try:
            from database import Client, Supplier, Employee, Project

            # عدد العملاء
            clients_count = self.session.query(Client).count()

            # عدد الموردين
            suppliers_count = self.session.query(Supplier).count()

            # عدد الموظفين
            employees_count = self.session.query(Employee).count()

            # عدد المشاريع النشطة
            active_projects = self.session.query(Project).filter(
                Project.status.in_(['قيد التنفيذ', 'جديد', 'مخطط'])
            ).count()

            # محاولة الحصول على عدد المنتجات في المخزون
            try:
                from database import Inventory
                inventory_count = self.session.query(Inventory).count()
            except ImportError:
                inventory_count = 0  # إذا لم يكن الجدول موجود

            # تحديث النص
            performance_text = f"📊 عملاء: {clients_count} | مشاريع: {active_projects}"
            self.business_stats_label.setText(performance_text)
            self.business_stats_label.setToolTip(
                f"مؤشرات الأداء:\n"
                f"• عدد العملاء: {clients_count}\n"
                f"• عدد الموردين: {suppliers_count}\n"
                f"• عدد الموظفين: {employees_count}\n"
                f"• المشاريع النشطة: {active_projects}\n"
                f"• منتجات المخزون: {inventory_count}"
            )

        except Exception as e:
            self.business_stats_label.setText("📊 خطأ في بيانات الأداء")
            print(f"خطأ في تحديث مؤشرات الأداء: {str(e)}")

    def update_smart_alerts(self):
        """تحديث التنبيهات الذكية"""
        try:
            from database import Invoice, Notification
            from datetime import datetime

            # التحقق من الفواتير المستحقة
            today = datetime.now().date()
            overdue_invoices = 0
            try:
                overdue_invoices = self.session.query(Invoice).filter(
                    Invoice.due_date < today,
                    Invoice.status != 'مدفوعة'
                ).count()
            except Exception:
                pass  # إذا لم يكن هناك جدول فواتير أو عمود due_date

            # التحقق من المصروفات المستحقة
            overdue_expenses = 0
            try:
                from database import Expense
                # محاولة التحقق من وجود عمود due_date
                if hasattr(Expense, 'due_date'):
                    overdue_expenses = self.session.query(Expense).filter(
                        Expense.due_date < today,
                        Expense.status != 'مدفوعة'
                    ).count()
            except Exception:
                pass  # إذا لم يكن هناك جدول مصروفات أو عمود due_date

            # التحقق من الإشعارات الجديدة
            notifications_count = 0
            try:
                notifications_count = self.session.query(Notification).count()
            except Exception:
                pass  # إذا لم يكن هناك جدول إشعارات

            # حساب إجمالي التنبيهات
            total_alerts = overdue_invoices + overdue_expenses + notifications_count

            # تحديد النص والأيقونة
            if total_alerts == 0:
                alert_text = "🔔 لا توجد تنبيهات"
            elif total_alerts <= 5:
                alert_text = f"⚠️ {total_alerts} تنبيهات"
            else:
                alert_text = f"🚨 {total_alerts} تنبيه مهم"

            self.smart_alerts_label.setText(alert_text)
            self.smart_alerts_label.setProperty("alerts", True)
            self.smart_alerts_label.setToolTip(
                f"التنبيهات والإشعارات:\n"
                f"• فواتير متأخرة: {overdue_invoices}\n"
                f"• مصروفات متأخرة: {overdue_expenses}\n"
                f"• إشعارات جديدة: {notifications_count}\n"
                f"• إجمالي التنبيهات: {total_alerts}"
            )

        except Exception as e:
            self.smart_alerts_label.setText("🔔 خطأ في التنبيهات")
            print(f"خطأ في تحديث التنبيهات الذكية: {str(e)}")

    def update_system_info(self):
        """تحديث معلومات النظام"""
        try:
            import psutil
            from sqlalchemy import text

            # التحقق من حالة قاعدة البيانات
            try:
                self.session.execute(text("SELECT 1"))
                db_status = "متصل ✅"
                db_icon = "💾"
            except:
                db_status = "غير متصل ❌"
                db_icon = "⚠️"

            # الحصول على استخدام الذاكرة
            try:
                memory_percent = psutil.virtual_memory().percent
                if memory_percent < 70:
                    memory_icon = "🟢"
                elif memory_percent < 85:
                    memory_icon = "🟡"
                else:
                    memory_icon = "🔴"
                memory_text = f"{memory_percent:.0f}%"
            except:
                memory_icon = "💾"
                memory_text = "غير متاح"

            # تحديث النص
            system_text = f"{db_icon} {db_status.split()[0]}"
            self.system_info_label.setText(system_text)
            self.system_info_label.setToolTip(
                f"معلومات النظام:\n"
                f"• قاعدة البيانات: {db_status}\n"
                f"• استخدام الذاكرة: {memory_icon} {memory_text}\n"
                f"• حالة النظام: نشط"
            )

        except Exception as e:
            self.system_info_label.setText("💾 خطأ في النظام")
            print(f"خطأ في تحديث معلومات النظام: {str(e)}")

    def update_datetime_only(self):
        """تحديث الوقت والتاريخ مع معلومات إضافية"""
        try:
            from utils import format_datetime
            now = QDateTime.currentDateTime().toPyDateTime()
            formatted_datetime = format_datetime(now)

            # تحديد الأيقونة حسب الوقت
            hour = now.hour
            if 6 <= hour < 12:
                time_icon = "🌅"  # صباح
                period_text = "صباح الخير"
            elif 12 <= hour < 18:
                time_icon = "☀️"  # ظهر
                period_text = "نهارك سعيد"
            elif 18 <= hour < 22:
                time_icon = "🌆"  # مساء
                period_text = "مساء الخير"
            else:
                time_icon = "🌙"  # ليل
                period_text = "ليلة سعيدة"

            # إضافة معلومات إضافية
            day_name = self.get_arabic_day_name(now.weekday())

            # تحديث النص مع معلومات إضافية
            display_text = f"{time_icon} {formatted_datetime}"
            self.datetime_label.setText(display_text)

            # تحديث التلميح مع معلومات مفصلة
            tooltip_text = (
                f"{period_text}\n"
                f"اليوم: {day_name}\n"
                f"التاريخ: {now.strftime('%Y/%m/%d')}\n"
                f"الوقت: {now.strftime('%H:%M:%S')}\n"
                f"المنطقة الزمنية: UTC+2"
            )
            self.datetime_label.setToolTip(tooltip_text)

        except Exception as e:
            self.datetime_label.setText("🕐 خطأ في التاريخ")
            print(f"خطأ في تحديث التاريخ والوقت: {str(e)}")

    def get_arabic_day_name(self, weekday):
        """الحصول على اسم اليوم بالعربية"""
        arabic_days = {
            0: "الاثنين",
            1: "الثلاثاء",
            2: "الأربعاء",
            3: "الخميس",
            4: "الجمعة",
            5: "السبت",
            6: "الأحد"
        }
        return arabic_days.get(weekday, "غير معروف")

    def update_status(self):
        """دالة التوافق مع الكود القديم - تحديث شريط الحالة المحفوظ"""
        try:
            if hasattr(self, 'saved_status_bar') and self.saved_status_bar:
                self.saved_status_bar.update_status_info()
            else:
                # إذا لم يكن شريط الحالة المحفوظ متاحاً، لا تفعل شيئاً
                pass
        except Exception as e:
            print(f"خطأ في تحديث شريط الحالة: {str(e)}")

    def check_database_status(self):
        """دالة التوافق مع الكود القديم - تستدعي تحديث معلومات النظام"""
        self.update_system_info()

    # ===== دوال المميزات الجديدة لشريط الحالة =====

    def start_performance_monitoring(self):
        """بدء مراقبة أداء النظام"""
        try:
            # مؤقت لمراقبة الأداء كل 30 ثانية
            self.performance_timer = QTimer()
            self.performance_timer.timeout.connect(self.update_performance_indicator)
            self.performance_timer.start(30000)  # كل 30 ثانية

            # تحديث فوري
            self.update_performance_indicator()

        except Exception as e:
            print(f"خطأ في بدء مراقبة الأداء: {str(e)}")

    def update_performance_indicator(self):
        """تحديث مؤشر أداء النظام"""
        try:
            import psutil
            import time

            # قياس استخدام المعالج
            cpu_percent = psutil.cpu_percent(interval=1)

            # قياس استخدام الذاكرة
            memory_percent = psutil.virtual_memory().percent

            # قياس سرعة الاستجابة (محاكاة)
            start_time = time.time()
            # محاولة استعلام بسيط لقياس سرعة قاعدة البيانات
            try:
                from sqlalchemy import text
                self.session.execute(text("SELECT 1"))
                db_response_time = (time.time() - start_time) * 1000  # بالميلي ثانية
            except:
                db_response_time = 999  # خطأ في قاعدة البيانات

            # تحديد حالة الأداء
            if cpu_percent < 50 and memory_percent < 70 and db_response_time < 100:
                performance_text = "🚀 ممتاز"
            elif cpu_percent < 70 and memory_percent < 85 and db_response_time < 500:
                performance_text = "⚡ جيد"
            else:
                performance_text = "⚠️ بطيء"

            # تحديث المؤشر
            self.performance_indicator.setText(performance_text)
            self.performance_indicator.setToolTip(
                f"أداء النظام:\n"
                f"• المعالج: {cpu_percent:.1f}%\n"
                f"• الذاكرة: {memory_percent:.1f}%\n"
                f"• قاعدة البيانات: {db_response_time:.0f}ms"
            )

        except Exception as e:
            self.performance_indicator.setText("❓ غير متاح")
            print(f"خطأ في تحديث مؤشر الأداء: {str(e)}")

    def add_recent_activity(self, activity_text):
        """إضافة نشاط جديد لقائمة الأنشطة الحديثة"""
        try:
            from datetime import datetime

            # إضافة النشاط مع الوقت
            timestamp = datetime.now().strftime("%H:%M")
            activity_with_time = f"{timestamp} - {activity_text}"

            # إضافة للقائمة (الحد الأقصى 10 أنشطة)
            self.recent_activities.insert(0, activity_with_time)
            if len(self.recent_activities) > 10:
                self.recent_activities = self.recent_activities[:10]

            # تحديث المؤشر
            if self.recent_activities:
                latest_activity = self.recent_activities[0]
                # اختصار النص إذا كان طويلاً
                if len(latest_activity) > 20:
                    display_text = f"📋 {latest_activity[:17]}..."
                else:
                    display_text = f"📋 {latest_activity}"

                self.recent_activity_label.setText(display_text)

                # تحديث التلميح بجميع الأنشطة
                tooltip_text = "آخر الأنشطة:\n" + "\n".join(self.recent_activities[:5])
                self.recent_activity_label.setToolTip(tooltip_text)

        except Exception as e:
            print(f"خطأ في إضافة النشاط الحديث: {str(e)}")

    def show_progress(self, text, value=0):
        """عرض مؤشر التقدم مع نص"""
        try:
            self.general_progress.setVisible(True)
            self.general_progress.setValue(value)
            self.operation_status_label.setText(f"⏳ {text}")

        except Exception as e:
            print(f"خطأ في عرض مؤشر التقدم: {str(e)}")

    def hide_progress(self):
        """إخفاء مؤشر التقدم"""
        try:
            self.general_progress.setVisible(False)
            self.operation_status_label.setText("⚡ جاهز")

        except Exception as e:
            print(f"خطأ في إخفاء مؤشر التقدم: {str(e)}")

    def toggle_auto_update(self):
        """تشغيل/إيقاف التحديث التلقائي"""
        try:
            if self.auto_update_btn.isChecked():
                # تشغيل التحديث التلقائي
                if hasattr(self, 'advanced_status_timer'):
                    self.advanced_status_timer.start(60000)
                if hasattr(self, 'datetime_timer'):
                    self.datetime_timer.start(30000)
                self.add_recent_activity("تم تشغيل التحديث التلقائي")
            else:
                # إيقاف التحديث التلقائي
                if hasattr(self, 'advanced_status_timer'):
                    self.advanced_status_timer.stop()
                if hasattr(self, 'datetime_timer'):
                    self.datetime_timer.stop()
                self.add_recent_activity("تم إيقاف التحديث التلقائي")

        except Exception as e:
            print(f"خطأ في تبديل التحديث التلقائي: {str(e)}")

    def toggle_notifications(self):
        """تشغيل/إيقاف الإشعارات"""
        try:
            if self.notifications_btn.isChecked():
                self.add_recent_activity("تم تشغيل الإشعارات")
                # يمكن إضافة منطق تشغيل الإشعارات هنا
            else:
                self.add_recent_activity("تم إيقاف الإشعارات")
                # يمكن إضافة منطق إيقاف الإشعارات هنا

        except Exception as e:
            print(f"خطأ في تبديل الإشعارات: {str(e)}")

    def toggle_theme(self):
        """تبديل الوضع المظلم/الفاتح"""
        try:
            if self.theme_btn.isChecked():
                self.theme_btn.setText("☀️")
                self.theme_btn.setToolTip("تبديل للوضع الفاتح")
                self.add_recent_activity("تم التبديل للوضع المظلم")
                # يمكن إضافة منطق تطبيق الوضع المظلم هنا
            else:
                self.theme_btn.setText("🌙")
                self.theme_btn.setToolTip("تبديل للوضع المظلم")
                self.add_recent_activity("تم التبديل للوضع الفاتح")
                # يمكن إضافة منطق تطبيق الوضع الفاتح هنا

        except Exception as e:
            print(f"خطأ في تبديل الوضع: {str(e)}")





    # تم حذف دالة create_toolbar القديمة التي كانت تسبب مشاكل في الجانب الأيمن
    # تم استبدالها بـ create_simple_effective_toolbar

    # تم حذف جميع الكود القديم للشريط الجانبي

    def create_tabs(self):
        # إنشاء التبويبات الرئيسية
        self.tabs = QTabWidget()
        self.tabs.setTabPosition(QTabWidget.North)  # وضع التبويبات في الأعلى
        self.tabs.tabBar().setVisible(False)  # إخفاء شريط التبويبات

        # تحسين تصميم التبويبات (مخفية)
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: #ecf0f1;
                margin: 0px;
                padding: 0px;
            }
            QTabBar::tab {
                background: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
            }
            QWidget {
                background: #ecf0f1;
            }
        """)

        self.setCentralWidget(self.tabs)

        # إضافة تبويبات للوحدات المختلفة
        # 1. تم إزالة لوحة المعلومات - إضافة تبويب فارغ للحفاظ على الترتيب
        empty_dashboard = QWidget()
        empty_layout = QVBoxLayout()
        empty_layout.setContentsMargins(50, 50, 50, 50)

        empty_label = QLabel("تم إزالة لوحة المعلومات")
        empty_label.setAlignment(Qt.AlignCenter)
        empty_label.setStyleSheet("color: #666; font-size: 16px; font-weight: bold;")
        empty_layout.addWidget(empty_label)

        empty_dashboard.setLayout(empty_layout)
        self.tabs.addTab(empty_dashboard, "لوحة المعلومات")

        # 2. العملاء
        if self.check_permission("can_manage_clients"):
            self.clients_widget = ClientsWidget(self.session)
            self.tabs.addTab(self.clients_widget, "العملاء")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "العملاء")
            self.tabs.setTabEnabled(1, False)

        # 3. الموردين
        if self.check_permission("can_manage_suppliers"):
            self.suppliers_widget = SuppliersWidget(self.session)
            self.tabs.addTab(self.suppliers_widget, "الموردين")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "الموردين")
            self.tabs.setTabEnabled(2, False)

        # 4. الموظفين (تم نقله من المكان السابق)
        if self.check_permission("can_manage_employees"):
            self.employees_widget = EmployeesWidget(self.session)
            self.tabs.addTab(self.employees_widget, "الموظفين")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "الموظفين")
            self.tabs.setTabEnabled(3, False)

        # 5. المشاريع
        if self.check_permission("can_manage_projects"):
            self.projects_widget = ProjectsWidget(self.session)
            self.tabs.addTab(self.projects_widget, "المشاريع")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "المشاريع")
            self.tabs.setTabEnabled(4, False)

        # 5. إدارة المخازن والمبيعات والمشتريات
        try:
            from ui.inventory import InventoryMainWidget
            self.inventory_widget = InventoryMainWidget(self.session)
            self.tabs.addTab(self.inventory_widget, "إدارة المخازن")
            print("✅ تم تحميل قسم إدارة المخازن بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل إدارة المخازن: {str(e)}")
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "إدارة المخازن")
            self.tabs.setTabEnabled(5, False)

        # 6. المصروفات
        if self.check_permission("can_manage_expenses"):
            self.expenses_widget = ExpensesWidget(self.session)
            self.tabs.addTab(self.expenses_widget, "المصروفات")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "المصروفات")
            self.tabs.setTabEnabled(6, False)

        # 7. الإيرادات
        if self.check_permission("can_manage_revenues"):
            self.revenues_widget = RevenuesWidget(self.session)
            self.tabs.addTab(self.revenues_widget, "الإيرادات")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "الإيرادات")
            self.tabs.setTabEnabled(7, False)

        # 8. الفواتير
        if self.check_permission("can_manage_invoices"):
            self.invoices_widget = InvoicesWidget(self.session)
            self.tabs.addTab(self.invoices_widget, "الفواتير")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "الفواتير")
            self.tabs.setTabEnabled(8, False)

        # 9. الإشعارات
        # الإشعارات متاحة للجميع (تحتوي على تبويب التنبيهات بداخلها)
        self.notifications_widget = NotificationsWidget(self.session)
        self.tabs.addTab(self.notifications_widget, "الإشعارات")

        # 10. التقارير
        if self.check_permission("can_view_reports"):
            self.reports_widget = ReportsWidget(self.session)
            self.tabs.addTab(self.reports_widget, "التقارير")
        else:
            # إضافة تبويب فارغ للحفاظ على ترتيب التبويبات
            empty_widget = QWidget()
            self.tabs.addTab(empty_widget, "التقارير")
            self.tabs.setTabEnabled(9, False)

    def load_tab_on_demand(self, index):
        """تحميل التبويب عند الطلب لتحسين الأداء"""
        try:
            # تحميل لوحة المعلومات المتطورة عند الطلب
            if index == 0 and self.dashboard_widget is None and self.check_permission("can_view_dashboard"):
                print("🔄 تحميل لوحة المعلومات المتطورة...")
                try:
                    from ui.clean_dashboard import CleanAdvancedDashboard
                    self.dashboard_widget = CleanAdvancedDashboard()
                    self.tabs.removeTab(0)
                    self.tabs.insertTab(0, self.dashboard_widget, "لوحة المعلومات")
                    print("✅ تم تحميل لوحة المعلومات المتطورة بنجاح")
                except Exception as dashboard_error:
                    print(f"❌ فشل في تحميل لوحة المعلومات المتطورة: {dashboard_error}")
                    # إنشاء لوحة معلومات بديلة
                    self.dashboard_widget = self.create_emergency_dashboard()
                    self.tabs.removeTab(0)
                    self.tabs.insertTab(0, self.dashboard_widget, "لوحة المعلومات")
                    print("✅ تم تحميل لوحة المعلومات البديلة")
        except Exception as e:
            print(f"خطأ في تحميل التبويب: {str(e)}")

    def create_emergency_dashboard(self):
        """إنشاء لوحة معلومات بديلة في حالة فشل تحميل لوحة المعلومات المتطورة"""
        print("🚨 إنشاء لوحة معلومات بديلة...")

        try:
            # محاولة إنشاء لوحة معلومات بسيطة
            from ui.clean_dashboard import CleanAdvancedDashboard
            dashboard_widget = CleanAdvancedDashboard()
            print("✅ تم إنشاء لوحة المعلومات البديلة بنجاح!")
            return dashboard_widget
        except Exception as e:
            print(f"❌ فشل في إنشاء لوحة المعلومات البديلة: {e}")

            # إنشاء widget بسيط كحل أخير
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QFont

            widget = QWidget()
            layout = QVBoxLayout()
            layout.setContentsMargins(30, 30, 30, 30)

            # رسالة بسيطة
            message = QLabel("لم يتم إنشاء لوحة المعلومات")
            message.setFont(QFont("Arial", 16, QFont.Bold))
            message.setAlignment(Qt.AlignCenter)
            message.setStyleSheet("color: #666; padding: 20px;")
            layout.addWidget(message)

            layout.addStretch()
            widget.setLayout(layout)

            print("✅ تم إنشاء widget بسيط كحل أخير!")
            return widget

    def load_dashboard_immediately(self):
        """تحميل لوحة المعلومات المتطورة فوراً"""
        print("🚀 تحميل لوحة المعلومات المتطورة فوراً...")
        try:
            if self.dashboard_widget is None and self.check_permission("can_view_dashboard"):
                print("🔄 إنشاء لوحة المعلومات المتطورة...")
                try:
                    from ui.clean_dashboard import CleanAdvancedDashboard
                    self.dashboard_widget = CleanAdvancedDashboard()
                    self.tabs.removeTab(0)
                    self.tabs.insertTab(0, self.dashboard_widget, "لوحة المعلومات")
                    print("✅ تم تحميل لوحة المعلومات المتطورة بنجاح")
                except Exception as dashboard_error:
                    print(f"❌ فشل في تحميل لوحة المعلومات المتطورة: {dashboard_error}")
                    # إنشاء لوحة معلومات بديلة
                    self.dashboard_widget = self.create_emergency_dashboard()
                    self.tabs.removeTab(0)
                    self.tabs.insertTab(0, self.dashboard_widget, "لوحة المعلومات")
                    print("✅ تم تحميل لوحة المعلومات البديلة")
        except Exception as e:
            print(f"❌ خطأ في تحميل لوحة المعلومات فوراً: {e}")

    def switch_tab(self, index, action, h_action=None):
        """تبديل التبويب وتحديث حالة أزرار شريط الأدوات"""
        try:
            # تحميل التبويب عند الطلب
            self.load_tab_on_demand(index)

            # التحقق من صحة المؤشر
            if index < 0 or index >= self.tabs.count():
                print(f"خطأ: مؤشر التبويب غير صالح: {index}")
                return

            # التحقق من وجود شريط الأدوات الرئيسي وتحديثه
            if hasattr(self, 'toolbar_actions'):
                try:
                    # إلغاء تحديد جميع الأزرار في شريط الأدوات الرئيسي
                    for act in self.toolbar_actions:
                        if act != action:
                            act.setChecked(False)

                    # تحديد الزر الحالي في شريط الأدوات الرئيسي
                    if action:
                        action.setChecked(True)
                    elif index < len(self.toolbar_actions):
                        # إذا لم يتم تمرير إجراء، حدد الزر المقابل في شريط الأدوات الرئيسي
                        self.toolbar_actions[index].setChecked(True)
                except Exception as e:
                    print(f"خطأ في تحديث شريط الأدوات الرئيسي: {str(e)}")

            # التحقق من وجود شريط الأدوات الأفقي الجديد وتحديثه (مع تثبيت المواضع)
            if hasattr(self, 'h_toolbar_buttons'):
                try:
                    # إذا تم تمرير زر من شريط الأدوات الأفقي
                    if h_action:
                        # إلغاء تحديد جميع الأزرار في شريط الأدوات الأفقي
                        for btn in self.h_toolbar_buttons:
                            if btn != h_action:
                                btn.setChecked(False)

                        # تحديد الزر الحالي في شريط الأدوات الأفقي
                        h_action.setChecked(True)
                    elif index < len(self.h_toolbar_buttons):
                        # إذا تم النقر على زر في شريط الأدوات الرئيسي، حدد الزر المقابل في شريط الأدوات الأفقي
                        # إلغاء تحديد جميع الأزرار في شريط الأدوات الأفقي
                        for btn in self.h_toolbar_buttons:
                            btn.setChecked(False)

                        # تحديد الزر المقابل في شريط الأدوات الأفقي
                        self.h_toolbar_buttons[index].setChecked(True)

                    # تثبيت مواضع الأزرار لمنع تغيير المكان
                    self.fix_toolbar_button_positions()
                except Exception as e:
                    print(f"خطأ في تحديث شريط الأدوات الأفقي: {str(e)}")

            # تبديل التبويب
            try:
                current_index = self.tabs.currentIndex()
                if current_index != index:
                    # تعيين التبويب الجديد
                    self.tabs.setCurrentIndex(index)

                    # تحديث التبويب الجديد فوراً مع الحفاظ على التنسيق
                    current_tab = self.tabs.widget(index)
                    if current_tab:
                        # الحفاظ على التنسيق عند التنقل - لا نحدث البيانات تلقائياً
                        # تم تعطيل التحديث التلقائي للحفاظ على الخطوط والتنسيق
                        # if hasattr(current_tab, 'refresh_data'):
                        #     try:
                        #         current_tab.refresh_data()
                        #     except Exception as e:
                        #         print(f"خطأ في تحديث بيانات التبويب: {str(e)}")

                        # تحميل الإعدادات فقط إذا لزم الأمر
                        if hasattr(current_tab, 'load_settings'):
                            try:
                                current_tab.load_settings()
                            except Exception as e:
                                print(f"خطأ في تحميل إعدادات التبويب: {str(e)}")

                        # الحفاظ على التنسيق الحالي
                        if hasattr(current_tab, 'preserve_formatting'):
                            try:
                                current_tab.preserve_formatting()
                            except Exception as e:
                                print(f"خطأ في الحفاظ على التنسيق: {str(e)}")

                        # لا نفرض التحديث لتجنب الوميض
                        # current_tab.update()
                        # current_tab.repaint()

            except Exception as e:
                print(f"خطأ في تبديل التبويب: {str(e)}")

            # تحديث شريط الحالة إذا كان موجودًا
            if hasattr(self, 'status_message_label'):
                try:
                    tab_name = self.tabs.tabText(index)
                    self.status_message_label.setText(f"📍 القسم الحالي: {tab_name}")
                except Exception as e:
                    print(f"خطأ في تحديث شريط الحالة: {str(e)}")

            # تحديث مؤشر القسم الحالي في شريط الحالة المحفوظ
            if hasattr(self, 'saved_status_bar') and self.saved_status_bar:
                try:
                    tab_name = self.tabs.tabText(index)
                    self.saved_status_bar.update_current_section(tab_name)
                except Exception as e:
                    print(f"خطأ في تحديث مؤشر القسم في شريط الحالة المحفوظ: {str(e)}")

            # فرض تحديث الواجهة الكامل لمنع مشاكل الشفافية
            self.force_ui_refresh()

        except Exception as e:
            # معالجة الأخطاء غير المتوقعة
            print(f"خطأ في تبديل التبويب: {str(e)}")
            # فرض تحديث النافذة في حالة حدوث خطأ
            try:
                self.update()
                self.repaint()
            except:
                pass

    def check_notifications(self):
        """التحقق من الإشعارات وتحديث شريط الحالة"""
        try:
            # التحقق من الفواتير المستحقة وإنشاء إشعارات لها
            from utils import check_due_invoices, check_due_reminders
            due_invoices = check_due_invoices(self.session)

            # التحقق من التنبيهات المستحقة وإنشاء إشعارات لها
            due_reminders = check_due_reminders(self.session)

            # تحديث عدد الإشعارات في شريط الحالة المتطور
            if hasattr(self, 'smart_alerts_label'):
                self.update_smart_alerts()

            # تحديث شريط الحالة
            if hasattr(self, 'financial_stats_label'):
                if due_invoices and due_reminders:
                    self.financial_stats_label.setText(f"💰 {len(due_invoices)} فواتير + {len(due_reminders)} تنبيهات مستحقة")
                elif due_invoices:
                    self.financial_stats_label.setText(f"💰 {len(due_invoices)} فواتير مستحقة الدفع")
                elif due_reminders:
                    self.financial_stats_label.setText(f"💰 {len(due_reminders)} تنبيهات مستحقة")

        except Exception as e:
            print(f"خطأ في التحقق من الإشعارات: {str(e)}")





    def show_settings_message(self):
        """عرض رسالة أن الإعدادات غير متاحة"""
        QMessageBox.information(
            self,
            "الإعدادات",
            "قسم الإعدادات غير متاح حالياً.\n"
            "تم إزالة جميع إعدادات الثيم والمظهر من البرنامج."
        )



    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # حفظ بيانات اليوميات قبل إغلاق البرنامج
            if hasattr(self, 'employees_widget') and self.employees_widget:
                try:
                    print("جاري حفظ بيانات اليوميات...")
                    self.employees_widget.save_daily_wages_data()
                    print("تم حفظ بيانات اليوميات بنجاح")
                except Exception as e:
                    print(f"خطأ في حفظ بيانات اليوميات: {str(e)}")

            # إغلاق جلسة قاعدة البيانات عند إغلاق البرنامج
            if hasattr(self, 'session') and self.session:
                try:
                    self.session.close()
                except Exception as e:
                    print(f"خطأ في إغلاق جلسة قاعدة البيانات: {str(e)}")

            # تنظيف الذاكرة
            try:
                import gc
                gc.collect()
            except Exception as e:
                print(f"خطأ في تنظيف الذاكرة: {str(e)}")

            # قبول حدث الإغلاق
            event.accept()
        except Exception as e:
            print(f"خطأ في معالجة حدث إغلاق النافذة: {str(e)}")
            # التأكد من قبول حدث الإغلاق حتى في حالة حدوث خطأ
            event.accept()

    def safe_refresh_tab(self, tab):
        """تحديث بيانات التبويب بشكل آمن"""
        try:
            if hasattr(tab, 'refresh_data') and callable(tab.refresh_data):
                tab.refresh_data()
        except Exception as e:
            print(f"خطأ في تحديث بيانات التبويب: {str(e)}")

    def safe_load_settings(self, tab):
        """تحميل إعدادات التبويب بشكل آمن"""
        try:
            if hasattr(tab, 'load_settings') and callable(tab.load_settings):
                tab.load_settings()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات التبويب: {str(e)}")

    def force_ui_refresh(self):
        """فرض تحديث الواجهة لمنع مشاكل الشفافية - معطل لتجنب الوميض"""
        try:
            # تم تعطيل التحديث المفرط لتجنب الوميض
            # self.update()
            # self.repaint()

            # تحديث بسيط فقط عند الضرورة
            pass

        except Exception as e:
            print(f"خطأ في فرض تحديث الواجهة: {str(e)}")

    def resizeEvent(self, event):
        """تحديث عرض شريط الأدوات الأفقي عند تغيير حجم النافذة"""
        super().resizeEvent(event)

        # تحديث عرض شريط الأدوات الأفقي الجديد ليناسب عرض النافذة
        if hasattr(self, 'h_toolbar_widget'):
            self.h_toolbar_widget.setMinimumWidth(self.width())
